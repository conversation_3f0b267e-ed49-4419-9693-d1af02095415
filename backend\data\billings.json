[{"id": 1, "invoice_number": "INV00001", "patient_id": 6, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 1, "price": 150, "amount": 150}, {"id": 3, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}], "subtotal": 2650, "discount": 10.47, "tax": 477.0, "total_amount": 3116.53, "paid_amount": 3116.53, "balance": 0, "payment_method": "UPI", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-27", "due_date": "2025-05-27", "created_at": "2025-04-27T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.340256", "tenant_id": 1, "created_by": 2, "sid_number": "MYD001"}, {"id": 2, "invoice_number": "INV00002", "patient_id": 29, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 2, "price": 600, "amount": 1200}, {"id": 2, "test_id": 5, "test_name": "Liver Function Test", "quantity": 3, "price": 800, "amount": 2400}, {"id": 3, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}], "subtotal": 4050, "discount": 475.17, "tax": 729.0, "total_amount": 4303.83, "paid_amount": 0, "balance": 4303.83, "payment_method": "Card", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-08", "due_date": "2025-06-07", "created_at": "2025-05-08T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.341183", "tenant_id": 3, "created_by": 3, "sid_number": "TNJ002"}, {"id": 3, "invoice_number": "INV00003", "patient_id": 33, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}], "subtotal": 1350, "discount": 94.44, "tax": 243.0, "total_amount": 1498.56, "paid_amount": 1273.46, "balance": 225.0999999999999, "payment_method": "Cash", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-04", "due_date": "2025-05-04", "created_at": "2025-04-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.341183", "tenant_id": 2, "created_by": 1, "sid_number": "SKZ003"}, {"id": 4, "invoice_number": "INV00004", "patient_id": 12, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}, {"id": 2, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 1, "price": 850, "amount": 850}], "subtotal": 1200, "discount": 94.55, "tax": 216.0, "total_amount": 1321.45, "paid_amount": 1321.45, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-04", "due_date": "2025-05-04", "created_at": "2025-04-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.341183", "tenant_id": 3, "created_by": 1, "sid_number": "TNJ004"}, {"id": 5, "invoice_number": "INV00005", "patient_id": 31, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 2, "price": 600, "amount": 1200}, {"id": 2, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}, {"id": 3, "test_id": 9, "test_name": "Stool Routine", "quantity": 1, "price": 250, "amount": 250}], "subtotal": 1900, "discount": 28.52, "tax": 342.0, "total_amount": 2213.48, "paid_amount": 2213.48, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-12", "due_date": "2025-05-12", "created_at": "2025-04-12T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180", "tenant_id": 1, "created_by": 1, "sid_number": "MYD005"}, {"id": 6, "invoice_number": "INV00006", "patient_id": 31, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}, {"id": 2, "test_id": 4, "test_name": "Lipid Profile", "quantity": 2, "price": 600, "amount": 1200}, {"id": 3, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}, {"id": 4, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 3, "price": 700, "amount": 2100}], "subtotal": 4700, "discount": 441.76, "tax": 846.0, "total_amount": 5104.24, "paid_amount": 5104.24, "balance": 0, "payment_method": "Bank Transfer", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-18", "due_date": "2025-05-18", "created_at": "2025-04-18T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180", "tenant_id": 3, "created_by": 1, "sid_number": "TNJ006"}, {"id": 7, "invoice_number": "INV00007", "patient_id": 40, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}, {"id": 2, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 2, "price": 700, "amount": 1400}, {"id": 3, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}], "subtotal": 2550, "discount": 72.01, "tax": 459.0, "total_amount": 2936.99, "paid_amount": 0, "balance": 2936.99, "payment_method": "Bank Transfer", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-04-07", "due_date": "2025-05-07", "created_at": "2025-04-07T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ007"}, {"id": 8, "invoice_number": "INV00008", "patient_id": 14, "items": [{"id": 1, "test_id": 9, "test_name": "Stool Routine", "quantity": 1, "price": 250, "amount": 250}, {"id": 2, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}], "subtotal": 2050, "discount": 125.58, "tax": 369.0, "total_amount": 2293.42, "paid_amount": 2293.42, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-05-05", "due_date": "2025-06-04", "created_at": "2025-05-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.342180", "tenant_id": 2, "created_by": 2, "sid_number": "SKZ008"}, {"id": 9, "invoice_number": "INV00009", "patient_id": 50, "items": [{"id": 1, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 3, "price": 200, "amount": 600}, {"id": 2, "test_id": 9, "test_name": "Stool Routine", "quantity": 1, "price": 250, "amount": 250}], "subtotal": 850, "discount": 19.72, "tax": 153.0, "total_amount": 983.28, "paid_amount": 0, "balance": 983.28, "payment_method": "Card", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-03-23", "due_date": "2025-04-22", "created_at": "2025-03-23T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181", "tenant_id": 2, "created_by": 1, "sid_number": "SKZ009"}, {"id": 10, "invoice_number": "INV00010", "patient_id": 29, "items": [{"id": 1, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 2, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 1, "price": 200, "amount": 200}, {"id": 3, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}], "subtotal": 1650, "discount": 317.03, "tax": 297.0, "total_amount": 1629.97, "paid_amount": 0, "balance": 1629.97, "payment_method": "UPI", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-03-28", "due_date": "2025-04-27", "created_at": "2025-03-28T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181", "tenant_id": 3, "created_by": 3, "sid_number": "TNJ010"}, {"id": 11, "invoice_number": "INV00011", "patient_id": 13, "items": [{"id": 1, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}, {"id": 2, "test_id": 5, "test_name": "Liver Function Test", "quantity": 2, "price": 800, "amount": 1600}, {"id": 3, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 3, "price": 850, "amount": 2550}, {"id": 4, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}, {"id": 5, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 2, "price": 200, "amount": 400}], "subtotal": 5700, "discount": 1023.75, "tax": 1026.0, "total_amount": 5702.25, "paid_amount": 0, "balance": 5702.25, "payment_method": "Bank Transfer", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-07", "due_date": "2025-06-06", "created_at": "2025-05-07T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ011"}, {"id": 12, "invoice_number": "INV00012", "patient_id": 40, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}, {"id": 2, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 3, "price": 850, "amount": 2550}, {"id": 3, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 1, "price": 200, "amount": 200}], "subtotal": 3200, "discount": 514.27, "tax": 576.0, "total_amount": 3261.73, "paid_amount": 0, "balance": 3261.73, "payment_method": "Bank Transfer", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-10", "due_date": "2025-07-10", "created_at": "2025-06-10T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.343181", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ012"}, {"id": 13, "invoice_number": "INV00013", "patient_id": 42, "items": [{"id": 1, "test_id": 5, "test_name": "Liver Function Test", "quantity": 3, "price": 800, "amount": 2400}], "subtotal": 2400, "discount": 398.17, "tax": 432.0, "total_amount": 2433.83, "paid_amount": 0, "balance": 2433.83, "payment_method": "Card", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-05", "due_date": "2025-07-05", "created_at": "2025-06-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.344182", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ013"}, {"id": 14, "invoice_number": "INV00014", "patient_id": 23, "items": [{"id": 1, "test_id": 9, "test_name": "Stool Routine", "quantity": 3, "price": 250, "amount": 750}, {"id": 2, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}, {"id": 3, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 2, "price": 700, "amount": 1400}], "subtotal": 3950, "discount": 426.16, "tax": 711.0, "total_amount": 4234.84, "paid_amount": 0, "balance": 4234.84, "payment_method": "Cash", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-18", "due_date": "2025-06-17", "created_at": "2025-05-18T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.344182", "tenant_id": 2, "created_by": 2, "sid_number": "SKZ014"}, {"id": 15, "invoice_number": "INV00015", "patient_id": 31, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}, {"id": 2, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 3, "price": 200, "amount": 600}, {"id": 3, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}], "subtotal": 2800, "discount": 303.39, "tax": 504.0, "total_amount": 3000.61, "paid_amount": 0, "balance": 3000.61, "payment_method": "UPI", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-04", "due_date": "2025-06-03", "created_at": "2025-05-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.344182", "tenant_id": 1, "created_by": 3, "sid_number": "MYD015"}, {"id": 16, "invoice_number": "INV00016", "patient_id": 27, "items": [{"id": 1, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 1, "price": 700, "amount": 700}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}], "subtotal": 1000, "discount": 176.53, "tax": 180.0, "total_amount": 1003.47, "paid_amount": 1003.47, "balance": 0, "payment_method": "UPI", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-09", "due_date": "2025-05-09", "created_at": "2025-04-09T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181", "tenant_id": 1, "created_by": 1, "sid_number": "MYD016"}, {"id": 17, "invoice_number": "INV00017", "patient_id": 16, "items": [{"id": 1, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 2, "price": 700, "amount": 1400}, {"id": 2, "test_id": 7, "test_name": "Thyroid Profile", "quantity": 2, "price": 850, "amount": 1700}, {"id": 3, "test_id": 5, "test_name": "Liver Function Test", "quantity": 1, "price": 800, "amount": 800}, {"id": 4, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}, {"id": 5, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}], "subtotal": 5950, "discount": 885.62, "tax": 1071.0, "total_amount": 6135.38, "paid_amount": 6135.38, "balance": 0, "payment_method": "Card", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-03-30", "due_date": "2025-04-29", "created_at": "2025-03-30T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ017"}, {"id": 18, "invoice_number": "INV00018", "patient_id": 36, "items": [{"id": 1, "test_id": 4, "test_name": "Lipid Profile", "quantity": 1, "price": 600, "amount": 600}], "subtotal": 600, "discount": 25.32, "tax": 108.0, "total_amount": 682.68, "paid_amount": 0, "balance": 682.68, "payment_method": "Cash", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-06", "due_date": "2025-06-05", "created_at": "2025-05-06T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181", "tenant_id": 3, "created_by": 2, "sid_number": "TNJ018"}, {"id": 19, "invoice_number": "INV00019", "patient_id": 37, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}, {"id": 2, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 2, "price": 700, "amount": 1400}, {"id": 3, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 2, "price": 700, "amount": 1400}], "subtotal": 3250, "discount": 398.74, "tax": 585.0, "total_amount": 3436.26, "paid_amount": 1501.97, "balance": 1934.2900000000002, "payment_method": "UPI", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-29", "due_date": "2025-05-29", "created_at": "2025-04-29T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.345181", "tenant_id": 1, "created_by": 3, "sid_number": "MYD019"}, {"id": 20, "invoice_number": "INV00020", "patient_id": 23, "items": [{"id": 1, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 2, "price": 200, "amount": 400}, {"id": 2, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 3, "price": 700, "amount": 2100}, {"id": 3, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}, {"id": 4, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 1, "price": 200, "amount": 200}, {"id": 5, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}], "subtotal": 4850, "discount": 252.63, "tax": 873.0, "total_amount": 5470.37, "paid_amount": 5470.37, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-05", "due_date": "2025-05-05", "created_at": "2025-04-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.346191", "tenant_id": 1, "created_by": 1, "sid_number": "MYD020"}, {"id": 21, "invoice_number": "INV00021", "patient_id": 4, "items": [{"id": 1, "test_id": 5, "test_name": "Liver Function Test", "quantity": 3, "price": 800, "amount": 2400}, {"id": 2, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 1, "price": 200, "amount": 200}], "subtotal": 2600, "discount": 140.02, "tax": 468.0, "total_amount": 2927.98, "paid_amount": 2927.98, "balance": 0, "payment_method": "Bank Transfer", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-04-05", "due_date": "2025-05-05", "created_at": "2025-04-05T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.346191", "tenant_id": 2, "created_by": 1, "sid_number": "SKZ021"}, {"id": 22, "invoice_number": "INV00022", "patient_id": 22, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 3, "test_id": 5, "test_name": "Liver Function Test", "quantity": 2, "price": 800, "amount": 1600}, {"id": 4, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}], "subtotal": 3400, "discount": 616.45, "tax": 612.0, "total_amount": 3395.55, "paid_amount": 711.93, "balance": 2683.*************, "payment_method": "Card", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-03", "due_date": "2025-05-03", "created_at": "2025-04-03T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.346191", "tenant_id": 1, "created_by": 3, "sid_number": "MYD022"}, {"id": 23, "invoice_number": "INV00023", "patient_id": 28, "items": [{"id": 1, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 2, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 1, "price": 700, "amount": 700}, {"id": 3, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 2, "price": 200, "amount": 400}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}, {"id": 5, "test_id": 8, "test_name": "<PERSON><PERSON>", "quantity": 2, "price": 200, "amount": 400}], "subtotal": 2900, "discount": 179.42, "tax": 522.0, "total_amount": 3242.58, "paid_amount": 598.59, "balance": 2643.99, "payment_method": "Bank Transfer", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-03-27", "due_date": "2025-04-26", "created_at": "2025-03-27T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347183", "tenant_id": 1, "created_by": 2, "sid_number": "MYD023"}, {"id": 24, "invoice_number": "INV00024", "patient_id": 40, "items": [{"id": 1, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 3, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 3, "price": 700, "amount": 2100}, {"id": 4, "test_id": 3, "test_name": "HbA1c", "quantity": 3, "price": 450, "amount": 1350}], "subtotal": 4200, "discount": 284.55, "tax": 756.0, "total_amount": 4671.45, "paid_amount": 3954.97, "balance": 716.48, "payment_method": "Bank Transfer", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-03", "due_date": "2025-05-03", "created_at": "2025-04-03T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601", "tenant_id": 1, "created_by": 2, "sid_number": "MYD024"}, {"id": 25, "invoice_number": "INV00025", "patient_id": 20, "items": [{"id": 1, "test_id": 9, "test_name": "Stool Routine", "quantity": 1, "price": 250, "amount": 250}], "subtotal": 250, "discount": 7.14, "tax": 45.0, "total_amount": 287.86, "paid_amount": 89.33, "balance": 198.**************, "payment_method": "UPI", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-06-04", "due_date": "2025-07-04", "created_at": "2025-06-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601", "tenant_id": 1, "created_by": 2, "sid_number": "MYD025"}, {"id": 26, "invoice_number": "INV00026", "patient_id": 25, "items": [{"id": 1, "test_id": 3, "test_name": "HbA1c", "quantity": 2, "price": 450, "amount": 900}, {"id": 2, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 3, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 3, "price": 700, "amount": 2100}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 1, "price": 350, "amount": 350}, {"id": 5, "test_id": 4, "test_name": "Lipid Profile", "quantity": 1, "price": 600, "amount": 600}], "subtotal": 5000, "discount": 275.61, "tax": 900.0, "total_amount": 5624.39, "paid_amount": 1133.07, "balance": 4491.************, "payment_method": "Bank Transfer", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-05-28", "due_date": "2025-06-27", "created_at": "2025-05-28T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601", "tenant_id": 2, "created_by": 1, "sid_number": "SKZ026"}, {"id": 27, "invoice_number": "INV00027", "patient_id": 31, "items": [{"id": 1, "test_id": 6, "test_name": "Kidney Function Test", "quantity": 1, "price": 700, "amount": 700}], "subtotal": 700, "discount": 60.48, "tax": 126.0, "total_amount": 765.52, "paid_amount": 765.52, "balance": 765.52, "payment_method": "Bank Transfer", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-03-30", "due_date": "2025-04-29", "created_at": "2025-03-30T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.347601", "tenant_id": 1, "created_by": 3, "sid_number": "MYD027", "last_payment_date": "2025-07-16T13:14:16.221538", "last_payment_method": "Cash", "last_payment_reference": "79", "payment_history": [{"payment_date": "2025-07-16T13:14:16.221560", "amount": 765.52, "method": "Cash", "reference": "79", "notes": "", "processed_by": 4, "remaining_due": 0.0}]}, {"id": 28, "invoice_number": "INV00028", "patient_id": 4, "items": [{"id": 1, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 2, "test_id": 3, "test_name": "HbA1c", "quantity": 1, "price": 450, "amount": 450}], "subtotal": 750, "discount": 142.96, "tax": 135.0, "total_amount": 742.04, "paid_amount": 0, "balance": 742.04, "payment_method": "UPI", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-04-03", "due_date": "2025-05-03", "created_at": "2025-04-03T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.348638", "tenant_id": 1, "created_by": 2, "sid_number": "MYD028"}, {"id": 29, "invoice_number": "INV00029", "patient_id": 18, "items": [{"id": 1, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 1, "price": 700, "amount": 700}, {"id": 2, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 3, "price": 350, "amount": 1050}, {"id": 3, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 2, "price": 700, "amount": 1400}, {"id": 4, "test_id": 1, "test_name": "Complete Blood Count (CBC)", "quantity": 2, "price": 350, "amount": 700}, {"id": 5, "test_id": 4, "test_name": "Lipid Profile", "quantity": 3, "price": 600, "amount": 1800}], "subtotal": 5650, "discount": 476.17, "tax": 1017.0, "total_amount": 6190.83, "paid_amount": 0, "balance": 6190.83, "payment_method": "Bank Transfer", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-05-04", "due_date": "2025-06-03", "created_at": "2025-05-04T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.348638", "tenant_id": 3, "created_by": 1, "sid_number": "TNJ029"}, {"id": 30, "invoice_number": "INV00030", "patient_id": 4, "items": [{"id": 1, "test_id": 5, "test_name": "Liver Function Test", "quantity": 2, "price": 800, "amount": 1600}, {"id": 2, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 3, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 2, "price": 150, "amount": 300}, {"id": 4, "test_id": 10, "test_name": "Dengue NS1 Antigen", "quantity": 3, "price": 700, "amount": 2100}, {"id": 5, "test_id": 2, "test_name": "Blood Glucose Fasting", "quantity": 3, "price": 150, "amount": 450}], "subtotal": 4750, "discount": 594.54, "tax": 855.0, "total_amount": 5010.46, "paid_amount": 542.76, "balance": 4467.7, "payment_method": "Card", "payment_status": "Partial", "status": "Partial", "invoice_date": "2025-04-02", "due_date": "2025-05-02", "created_at": "2025-04-02T17:15:25.316838", "updated_at": "2025-06-25T14:41:56.348638", "tenant_id": 2, "created_by": 3, "sid_number": "SKZ030"}, {"id": 31, "invoice_number": "INV00031", "sid_number": "MYD003", "patient_id": 34, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750436899987, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 0, "balance": 944, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-20", "due_date": "2025-07-20", "notes": "", "branch": "1", "created_at": "2025-06-20T21:58:22.614664", "updated_at": "2025-06-25T14:43:56.710105", "tenant_id": 1, "created_by": 4}, {"id": 32, "invoice_number": "INV00032", "sid_number": "SKZ013", "patient_id": 37, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750490226713, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 944.0, "balance": 944, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "notes": "", "branch": "2", "created_at": "2025-06-21T12:47:09.145759", "updated_at": "2025-06-21T14:31:12.269097", "tenant_id": 2, "created_by": 5, "last_payment_date": "2025-07-16T13:12:36.897759", "last_payment_method": "Cash", "last_payment_reference": "76876", "payment_history": [{"payment_date": "2025-07-16T13:12:36.897775", "amount": 944.0, "method": "Cash", "reference": "76876", "notes": "", "processed_by": 4, "remaining_due": 0.0}]}, {"id": 33, "invoice_number": "INV00033", "sid_number": "MYD002", "patient_id": 34, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750495890087, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 0, "balance": 944, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "notes": "", "branch": "1", "created_at": "2025-06-21T14:21:32.518940", "updated_at": "2025-06-21T14:21:32.518940", "tenant_id": 1, "created_by": 4}, {"id": 34, "invoice_number": "INV00034", "sid_number": "SKZ001", "patient_id": 37, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750496536278, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 944.0, "balance": 0.0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "notes": "", "branch": "2", "created_at": "2025-06-21T14:32:20.748951", "updated_at": "2025-06-25T15:42:15.990422", "tenant_id": 2, "created_by": 5, "payments": [{"amount": 944.0, "payment_method": "Cash", "payment_date": "2025-06-25T15:42:15.990422", "reference": "", "notes": "", "collected_by": 5}]}, {"id": 35, "invoice_number": "INV00035", "sid_number": "SKZ015", "patient_id": 37, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750496601733, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}], "bill_amount": 800, "other_charges": 0, "discount_percent": 0, "subtotal": 944, "discount": 0, "gst_rate": 18, "gst_amount": 144, "tax": 144, "total_amount": 944, "paid_amount": 944.0, "balance": 0.0, "payment_method": "Cash", "payment_status": "Paid", "status": "Paid", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "notes": "", "branch": "2", "created_at": "2025-06-21T14:33:23.894705", "updated_at": "2025-06-25T15:41:45.508376", "tenant_id": 2, "created_by": 4, "payments": [{"amount": 944.0, "payment_method": "Cash", "payment_date": "2025-06-25T15:41:45.508376", "reference": "", "notes": "", "collected_by": 5}]}, {"id": 36, "invoice_number": "INV00036", "sid_number": "008", "patient_id": 28, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1750511358432, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}, {"testName": "Activated Protein C Resisitance", "test_id": 8, "amount": 3900, "id": 1750511363024, "test_name": "Activated Protein C Resisitance", "department": "IMMUNOLOGY", "hmsCode": "1429.0"}, {"testName": "ANDROSTENEDIONE (A4)", "test_id": 12, "amount": 900, "id": 1750511369104, "test_name": "ANDROSTENEDIONE (A4)", "department": "IMMUNOLOGY", "hmsCode": "512.0"}], "bill_amount": 5600, "other_charges": 0, "discount_percent": 0, "subtotal": 6608, "discount": 0, "gst_rate": 18, "gst_amount": 1008, "tax": 1008, "total_amount": 6608, "paid_amount": 6610, "balance": -2, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-06-21", "due_date": "2025-07-21", "notes": "", "branch": "1", "created_at": "2025-06-21T18:40:40.093362", "updated_at": "2025-06-21T18:40:40.093364", "tenant_id": 1, "created_by": 4}, {"id": 37, "invoice_number": "INV00037", "sid_number": "015", "patient_id": 51, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1751026392125, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}, {"testName": "<PERSON> Muller<PERSON> (AMH)", "test_id": 13, "amount": 1500, "id": 1751026396594, "test_name": "<PERSON> Muller<PERSON> (AMH)", "department": "IMMUNOLOGY", "hmsCode": "1305.0"}, {"testName": "IGF BP3", "test_id": 67, "amount": 2500, "id": 1751026400472, "test_name": "IGF BP3", "department": "IMMUNOLOGY", "hmsCode": "583.0"}, {"testName": "Estrone", "test_id": 45, "amount": 6500, "id": 1751*********, "test_name": "Estrone", "department": "IMMUNOLOGY", "hmsCode": "1499.0"}, {"testName": "IgE", "test_id": 66, "amount": 900, "id": 1751026417279, "test_name": "IgE", "department": "IMMUNOLOGY", "hmsCode": "637.0"}, {"testName": "BETA hCG", "test_id": 16, "amount": 600, "id": 1751026422735, "test_name": "BETA hCG", "department": "IMMUNOLOGY", "hmsCode": "522.0"}], "bill_amount": 12800, "other_charges": 0, "discount_percent": 0, "subtotal": 15104, "discount": 0, "gst_rate": 18, "gst_amount": 2304, "tax": 2304, "total_amount": 15104, "paid_amount": 16000, "balance": -896, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-06-27", "due_date": "2025-07-27", "notes": "", "branch": "1", "created_at": "2025-06-27T17:46:17.089903", "updated_at": "2025-06-27T17:46:17.089904", "tenant_id": 1, "created_by": 4}, {"id": 38, "invoice_number": "INV00038", "sid_number": "017", "patient_id": 51, "items": [{"testName": "Activated Protein C Resisitance", "test_id": 8, "amount": 3900, "id": 1751178484334, "test_name": "Activated Protein C Resisitance", "department": "IMMUNOLOGY", "hmsCode": "1429.0"}, {"testName": "ANDROSTENEDIONE (A4)", "test_id": 12, "amount": 900, "id": 1751178499717, "test_name": "ANDROSTENEDIONE (A4)", "department": "IMMUNOLOGY", "hmsCode": "512.0"}, {"testName": "ALPHA 1. ANTITRIPSIN", "test_id": 10, "amount": 1500, "id": 1751178504823, "test_name": "ALPHA 1. ANTITRIPSIN", "department": "IMMUNOLOGY", "hmsCode": "508.0"}], "bill_amount": 6300, "other_charges": 0, "discount_percent": 0, "subtotal": 7434, "discount": 0, "gst_rate": 18, "gst_amount": 1134, "tax": 1134, "total_amount": 7434, "paid_amount": 8000, "balance": -566, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-06-29", "due_date": "2025-07-29", "notes": "", "branch": "1", "created_at": "2025-06-29T11:59:27.311909", "updated_at": "2025-06-29T11:59:27.311910", "tenant_id": 1, "created_by": 4}, {"id": 39, "invoice_number": "INV00039", "sid_number": "020", "patient_id": 51, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1751783883632, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}, {"testName": "ACTH (Adreno Corticotropic Hormone)", "test_id": 7, "amount": 1200, "id": 1751783889978, "test_name": "ACTH (Adreno Corticotropic Hormone)", "department": "IMMUNOLOGY", "hmsCode": "17.0"}, {"testName": "ALPHA 1. ANTITRIPSIN", "test_id": 10, "amount": 1500, "id": 1751783895303, "test_name": "ALPHA 1. ANTITRIPSIN", "department": "IMMUNOLOGY", "hmsCode": "508.0"}, {"testName": "CA 72.4 ( TAG-72)", "test_id": 23, "amount": 1700, "id": 1751783902564, "test_name": "CA 72.4 ( TAG-72)", "department": "IMMUNOLOGY", "hmsCode": "665.0"}, {"testName": "<PERSON><PERSON><PERSON><PERSON>, Direct", "test_id": 237, "amount": 150, "id": 1751783920060, "test_name": "<PERSON><PERSON><PERSON><PERSON>, Direct", "department": "Biochemistry", "hmsCode": "217"}], "bill_amount": 5350, "other_charges": 0, "discount_percent": 0, "subtotal": 6313, "discount": 0, "gst_rate": 18, "gst_amount": 963, "tax": 963, "total_amount": 6313, "paid_amount": 4999.92, "balance": 1313.08, "payment_method": "", "payment_status": "Partial", "status": "Pending", "invoice_date": "2025-07-06", "due_date": "2025-08-05", "notes": "", "branch": "1", "created_at": "2025-07-06T12:14:39.004091", "updated_at": "2025-07-06T12:14:39.004096", "tenant_id": 1, "created_by": 4}, {"id": 40, "invoice_number": "INV00040", "sid_number": "025", "patient_id": 51, "items": [{"testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "amount": 800, "id": 1751872909476, "test_name": "17 - HYDROXY PROGESTERONE", "department": "IMMUNOLOGY", "hmsCode": "2.0"}, {"testName": "ANDROSTENEDIONE (A4)", "test_id": 12, "amount": 900, "id": 1751872913886, "test_name": "ANDROSTENEDIONE (A4)", "department": "IMMUNOLOGY", "hmsCode": "512.0"}, {"testName": "Alpha Fetoprotein (AFP)", "test_id": 11, "amount": 950, "id": 1751872918285, "test_name": "Alpha Fetoprotein (AFP)", "department": "IMMUNOLOGY", "hmsCode": "505.0"}], "bill_amount": 2650, "other_charges": 10, "discount_percent": 5, "subtotal": 2658.03, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 2658.03, "paid_amount": 2699.96, "balance": -41.929999999999836, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-07", "due_date": "2025-08-06", "notes": "", "branch": "1", "created_at": "2025-07-07T12:53:00.903498", "updated_at": "2025-07-07T12:53:00.903500", "tenant_id": 1, "created_by": 4}, {"id": 41, "invoice_number": "INV00041", "sid_number": "040", "patient_id": 1, "items": [{"test_id": 1, "test_name": "Blood Test", "quantity": 1, "price": 100, "amount": 100}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "notes": "", "branch": 1, "created_at": "2025-07-08T17:22:11.439341", "updated_at": "2025-07-08T17:22:11.439342", "tenant_id": 1, "created_by": 1}, {"id": 42, "invoice_number": "INV00042", "sid_number": "049", "patient_id": 1, "items": [{"test_id": 1, "test_name": "Blood Test", "quantity": 1, "price": 100, "amount": 100}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "notes": "", "branch": 1, "created_at": "2025-07-08T17:33:16.720431", "updated_at": "2025-07-08T17:33:16.720433", "tenant_id": 1, "created_by": 1}, {"id": 43, "invoice_number": "INV00043", "sid_number": "051", "patient_id": 1, "items": [{"test_id": 1, "test_name": "Blood Test", "quantity": 1, "price": 100, "amount": 100}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "notes": "", "branch": 1, "created_at": "2025-07-08T17:37:39.459516", "updated_at": "2025-07-08T17:37:39.459517", "tenant_id": 1, "created_by": 1}, {"id": 44, "invoice_number": "INV00044", "sid_number": "052", "patient_id": 1, "items": [{"test_id": 1, "test_name": "Blood Test", "quantity": 1, "price": 100, "amount": 100}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "notes": "", "branch": 1, "created_at": "2025-07-08T17:38:54.820842", "updated_at": "2025-07-08T17:38:54.820844", "tenant_id": 1, "created_by": 1}, {"id": 45, "invoice_number": "INV00045", "sid_number": "053", "patient_id": 1, "items": [{"test_id": 1, "test_name": "Blood Test", "quantity": 1, "price": 100, "amount": 100}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100, "paid_amount": 0, "balance": 100, "payment_method": "", "payment_status": "Pending", "status": "Pending", "invoice_date": "2025-07-08", "due_date": "2025-08-07", "notes": "", "branch": 1, "created_at": "2025-07-08T17:39:55.610404", "updated_at": "2025-07-08T17:39:55.610405", "tenant_id": 1, "created_by": 1}, {"id": 46, "invoice_number": "INV00046", "sid_number": "002", "patient_id": 1, "items": [{"test_id": 2, "test_name": "X-Ray", "amount": 200.0, "quantity": 1}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 200.0, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 200.0, "paid_amount": 200.0, "balance": 0.0, "payment_method": "Card", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "notes": "", "branch": 2, "created_at": "2025-07-09T16:25:23.898125", "updated_at": "2025-07-09T16:25:23.898127", "tenant_id": 2, "created_by": 5}, {"id": 47, "invoice_number": "INV00046", "sid_number": "001", "patient_id": 53, "items": [{"amount": 100, "quantity": 1, "test_id": 1, "test_name": "Blood Test"}], "bill_amount": 0, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 0, "gst_amount": 0, "tax": 0, "total_amount": 100, "paid_amount": 100, "balance": 0, "payment_method": "Cash", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "notes": "", "branch": 2, "created_at": "2025-07-09T16:25:58.970022", "updated_at": "2025-07-09T16:25:58.970024", "tenant_id": 2, "created_by": 5, "patient": {"first_name": "Test", "id": 53, "last_name": "Patient"}}, {"id": 51, "invoice_number": "INV00051", "sid_number": "116", "patient_id": 28, "items": [{"amount": 3500, "department": "IMMUNOLOGY", "hmsCode": "648.0", "id": 1752058998392, "testName": "1,25 Dihydroxyvitamin D", "test_id": 1, "test_name": "1,25 Dihydroxyvitamin D"}, {"amount": 800, "department": "IMMUNOLOGY", "hmsCode": "2.0", "id": 1752059003401, "testName": "17 - HYDROXY PROGESTERONE", "test_id": 2, "test_name": "17 - HYDROXY PROGESTERONE"}], "bill_amount": 4300, "other_charges": 0, "discount_percent": 0, "subtotal": 5074, "discount": 0, "gst_rate": 18, "gst_amount": 774, "tax": 774, "total_amount": 5074, "paid_amount": 5999.96, "balance": -925.96, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-09", "due_date": "2025-08-08", "notes": "", "branch": "1", "created_at": "2025-07-09T16:34:04.566473", "updated_at": "2025-07-09T16:34:04.566474", "tenant_id": 1, "created_by": 4, "patient": {"first_name": "<PERSON>", "id": 28, "last_name": "<PERSON><PERSON><PERSON>"}}, {"id": 55, "invoice_number": "INV00055", "sid_number": "208", "patient_id": 51, "items": [{"id": 1752572629249, "test_id": 76, "test_master_id": 76, "testName": "SICKLING TEST", "test_name": "SICKLING TEST", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000402", "display_name": "SICKLING TEST", "short_name": "SICK", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 76, "testName": "SICKLING TEST", "test_profile": "SICKLING TEST", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000402", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.647734", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "SICK", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000402", "test_done_on": "all", "test_name": "SICKLING TEST", "updated_at": "2025-07-09T12:20:55.647737"}}, {"id": 1752572633900, "test_id": 32, "test_master_id": 32, "testName": "Immature Platelet Fraction (IPF)", "test_name": "Immature Platelet Fraction (IPF)", "amount": 1000, "price": 1000, "test_price": 1000, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001537", "display_name": "Immature Platelet Fraction (IPF)", "short_name": "IPF", "international_code": "", "method": "Fluorescent Flow Cytometry", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "EDTA Container", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 5, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 32, "testName": "Immature Platelet Fraction (IPF)", "test_profile": "Immature Platelet Fraction (IPF)", "test_price": 1000, "department": "HAEMATOLOGY", "hmsCode": "001537", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": 5, "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "Fluorescent Flow Cytometry", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.642586", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 157, "min_sample_qty": null, "price": 1000, "reference_range": null, "reporting_days": 5, "result_type": "Pick List", "result_unit": null, "short_name": "IPF", "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "001537", "test_done_on": "all", "test_name": "Immature Platelet Fraction (IPF)", "updated_at": "2025-07-09T12:20:55.642589"}}], "bill_amount": 1100, "other_charges": 1100, "discount_percent": 0, "subtotal": 2596, "discount": 0, "gst_rate": 18, "gst_amount": 396, "tax": 396, "total_amount": 2596, "paid_amount": 3000, "balance": -404, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-15", "due_date": "2025-08-14", "notes": "", "branch": 1, "created_at": "2025-07-15T15:14:41.074226", "updated_at": "2025-07-15T15:14:41.074229", "tenant_id": 1, "created_by": 4}, {"id": 57, "invoice_number": "INV00057", "sid_number": "217", "patient_id": 59, "items": [{"id": 1752643063039, "test_id": 17, "test_master_id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "amount": 3600, "price": 3600, "test_price": 3600, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001599", "display_name": "DCP- Decarboxy Prothrombin PIVKA II", "short_name": "DCP", "international_code": "", "method": "CMIA", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "reference_range": "17.36 - 50.90", "result_unit": "mAU/ml", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 10, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_profile": "DCP- Decarboxy Prothrombin PIVKA II", "test_price": 3600, "department": "HAEMATOLOGY", "hmsCode": "001599", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "serviceTime": "", "reportingDays": 10, "cutoffTime": "", "referenceRange": "17.36 - 50.90", "resultUnit": "mAU/ml", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "CMIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 12, "created_at": "2025-07-09T12:20:55.640014", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 29, "min_sample_qty": null, "price": 3600, "reference_range": "17.36 - 50.90", "reporting_days": 10, "result_type": "-", "result_unit": "mAU/ml", "short_name": "DCP", "source_sheet": "HAEMATOLOGY", "specimen_code": 39, "test_code": "001599", "test_done_on": "all", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "updated_at": "2025-07-09T12:20:55.640017"}}, {"id": 1752643068450, "test_id": 12, "test_master_id": 12, "testName": "Chromosome Analysis - Product of Conception", "test_name": "Chromosome Analysis - Product of Conception", "amount": 6100, "price": 6100, "test_price": 6100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000922", "display_name": "Chromosome Analysis - Product of Conception", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 12, "testName": "Chromosome Analysis - Product of Conception", "test_profile": "Chromosome Analysis - Product of Conception", "test_price": 6100, "department": "HAEMATOLOGY", "hmsCode": "000922", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639476", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 6100, "reference_range": null, "reporting_days": 0, "result_type": "Template", "result_unit": null, "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000922", "test_done_on": "all", "test_name": "Chromosome Analysis - Product of Conception", "updated_at": "2025-07-09T12:20:55.639479"}}], "bill_amount": 9700, "other_charges": null, "discount_percent": 0, "subtotal": 9700, "discount": 0, "gst_rate": 18, "gst_amount": 1746, "tax": 1746, "total_amount": 9700, "paid_amount": 9700, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-16", "due_date": "2025-08-15", "notes": "", "branch": 1, "created_at": "2025-07-16T10:51:45.327764", "updated_at": "2025-07-16T10:51:45.327766", "tenant_id": 1, "created_by": 4}, {"id": 58, "invoice_number": "INV00058", "sid_number": "220", "patient_id": 59, "items": [{"id": 1752643892795, "test_id": 76, "test_master_id": 76, "testName": "SICKLING TEST", "test_name": "SICKLING TEST", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000402", "display_name": "SICKLING TEST", "short_name": "SICK", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 76, "testName": "SICKLING TEST", "test_profile": "SICKLING TEST", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000402", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.647734", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "SICK", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000402", "test_done_on": "all", "test_name": "SICKLING TEST", "updated_at": "2025-07-09T12:20:55.647737"}}], "bill_amount": 100, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 18, "gst_amount": 18, "tax": 18, "total_amount": 100, "paid_amount": 100, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-16", "due_date": "2025-08-15", "notes": "", "branch": 1, "created_at": "2025-07-16T11:02:04.702677", "updated_at": "2025-07-16T11:02:04.702679", "tenant_id": 1, "created_by": 4}, {"id": 59, "invoice_number": "INV00059", "sid_number": "222", "patient_id": 44, "items": [{"id": 1752674235940, "test_id": 407, "test_master_id": 407, "testName": "Alkaline phosphatase", "test_name": "Alkaline phosphatase", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "BIOCHEMISTRY", "hms_code": "000027", "display_name": "Alkaline phosphatase", "short_name": "ALP", "international_code": "", "method": "PNPP-DGKC", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "Children : 47 - 406 (Age and gender dependent)  Adults   : 80 - 306", "result_unit": "U/L", "decimals": 1, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 407, "testName": "Alkaline phosphatase", "test_profile": "Alkaline phosphatase", "test_price": 100, "department": "BIOCHEMISTRY", "hmsCode": "000027", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Children : 47 - 406 (Age and gender dependent)  Adults   : 80 - 306", "resultUnit": "U/L", "decimals": 1, "criticalLow": null, "criticalHigh": null, "method": "PNPP-DGKC", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.531537", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 38, "min_sample_qty": null, "price": 100, "reference_range": "Children : 47 - 406 (Age and gender dependent)  Adults   : 80 - 306", "reporting_days": 0, "result_type": "-", "result_unit": "U/L", "short_name": "ALP", "source_sheet": "BioChemistry", "specimen_code": 64, "test_code": "000027", "test_done_on": "all", "test_name": "Alkaline phosphatase", "updated_at": "2025-07-09T12:20:56.531540"}}, {"id": 1752674247500, "test_id": 119, "test_master_id": 119, "testName": "CHIKUNGUNYA , Qualitative PCR", "test_name": "CHIKUNGUNYA , Qualitative PCR", "amount": 3000, "price": 3000, "test_price": 3000, "quantity": 1, "department": "MOLECULAR_BIOLOGY", "hms_code": "001098", "display_name": "CHIKUNGUNYA , Qualitative PCR", "short_name": "CHIP", "international_code": "", "method": "PCR", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "EDTA Container", "reference_range": "NOT DETECTED.", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 119, "testName": "CHIKUNGUNYA , Qualitative PCR", "test_profile": "CHIKUNGUNYA , Qualitative PCR", "test_price": 3000, "department": "MOLECULAR_BIOLOGY", "hmsCode": "001098", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NOT DETECTED.", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "PCR", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.833314", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 117, "min_sample_qty": null, "price": 3000, "reference_range": "NOT DETECTED.", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "CHIP", "source_sheet": "Molecular Biology", "specimen_code": 17, "test_code": "001098", "test_done_on": "all", "test_name": "CHIKUNGUNYA , Qualitative PCR", "updated_at": "2025-07-09T12:20:55.833332"}}], "bill_amount": 3100, "other_charges": 0, "discount_percent": 0, "subtotal": 3100, "discount": 0, "gst_rate": 18, "gst_amount": 558, "tax": 558, "total_amount": 3100, "paid_amount": 3100, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-16", "due_date": "2025-08-15", "notes": "", "branch": 1, "created_at": "2025-07-16T19:28:03.623684", "updated_at": "2025-07-16T19:28:03.623685", "tenant_id": 1, "created_by": 4}, {"id": 60, "invoice_number": "INV00060", "sid_number": "234", "patient_id": 61, "items": [{"id": 1752759347094, "test_id": 48, "test_master_id": 48, "testName": "MYOGLOBIN-SERUM", "test_name": "MYOGLOBIN-SERUM", "amount": 1200, "price": 1200, "test_price": 1200, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000426", "display_name": "MYOGLOBIN-SERUM", "short_name": "MYOS", "international_code": "", "method": "ECLIA", "primary_specimen": "Serum", "specimen": "Serum", "container": "", "reference_range": "25-58", "result_unit": "ug/L", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 48, "testName": "MYOGLOBIN-SERUM", "test_profile": "MYOGLOBIN-SERUM", "test_price": 1200, "department": "HAEMATOLOGY", "hmsCode": "000426", "specimen": "Serum", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "25-58", "resultUnit": "ug/L", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "ECLIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644672", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 52, "min_sample_qty": null, "price": 1200, "reference_range": "25-58", "reporting_days": 0, "result_type": "Numeric", "result_unit": "ug/L", "short_name": "MYOS", "source_sheet": "HAEMATOLOGY", "specimen_code": 64, "test_code": "000426", "test_done_on": "all", "test_name": "MYOGLOBIN-SERUM", "updated_at": "2025-07-09T12:20:55.644675"}}, {"id": 1752759351845, "test_id": 17, "test_master_id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "amount": 3600, "price": 3600, "test_price": 3600, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001599", "display_name": "DCP- Decarboxy Prothrombin PIVKA II", "short_name": "DCP", "international_code": "", "method": "CMIA", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "reference_range": "17.36 - 50.90", "result_unit": "mAU/ml", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 10, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_profile": "DCP- Decarboxy Prothrombin PIVKA II", "test_price": 3600, "department": "HAEMATOLOGY", "hmsCode": "001599", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "serviceTime": "", "reportingDays": 10, "cutoffTime": "", "referenceRange": "17.36 - 50.90", "resultUnit": "mAU/ml", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "CMIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 12, "created_at": "2025-07-09T12:20:55.640014", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 29, "min_sample_qty": null, "price": 3600, "reference_range": "17.36 - 50.90", "reporting_days": 10, "result_type": "-", "result_unit": "mAU/ml", "short_name": "DCP", "source_sheet": "HAEMATOLOGY", "specimen_code": 39, "test_code": "001599", "test_done_on": "all", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "updated_at": "2025-07-09T12:20:55.640017"}}], "bill_amount": 4800, "other_charges": 0, "discount_percent": 0, "subtotal": 4800, "discount": 0, "gst_rate": 18, "gst_amount": 864, "tax": 864, "total_amount": 4800, "paid_amount": 5000, "balance": -200, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-17", "due_date": "2025-08-16", "notes": "", "branch": 1, "created_at": "2025-07-17T19:06:30.208055", "updated_at": "2025-07-17T19:06:30.208057", "tenant_id": 1, "created_by": 4}, {"id": 61, "invoice_number": "INV00061", "sid_number": "248", "patient_id": 51, "items": [{"id": 1752760666606, "test_id": 17, "test_master_id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "amount": 3600, "price": 3600, "test_price": 3600, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001599", "display_name": "DCP- Decarboxy Prothrombin PIVKA II", "short_name": "DCP", "international_code": "", "method": "CMIA", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "reference_range": "17.36 - 50.90", "result_unit": "mAU/ml", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 10, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_profile": "DCP- Decarboxy Prothrombin PIVKA II", "test_price": 3600, "department": "HAEMATOLOGY", "hmsCode": "001599", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "serviceTime": "", "reportingDays": 10, "cutoffTime": "", "referenceRange": "17.36 - 50.90", "resultUnit": "mAU/ml", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "CMIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 12, "created_at": "2025-07-09T12:20:55.640014", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 29, "min_sample_qty": null, "price": 3600, "reference_range": "17.36 - 50.90", "reporting_days": 10, "result_type": "-", "result_unit": "mAU/ml", "short_name": "DCP", "source_sheet": "HAEMATOLOGY", "specimen_code": 39, "test_code": "001599", "test_done_on": "all", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "updated_at": "2025-07-09T12:20:55.640017"}}], "bill_amount": 3600, "other_charges": 0, "discount_percent": 0, "subtotal": 3600, "discount": 0, "gst_rate": 18, "gst_amount": 648, "tax": 648, "total_amount": 3600, "paid_amount": 4000, "balance": -400, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-17", "due_date": "2025-08-16", "notes": "", "branch": 1, "created_at": "2025-07-17T19:27:50.352678", "updated_at": "2025-07-17T19:27:50.352680", "tenant_id": 1, "created_by": 4}, {"id": 62, "invoice_number": "INV00062", "sid_number": "251", "patient_id": 62, "items": [{"id": 1752821803827, "test_id": 76, "test_master_id": 76, "testName": "SICKLING TEST", "test_name": "SICKLING TEST", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000402", "display_name": "SICKLING TEST", "short_name": "SICK", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 76, "testName": "SICKLING TEST", "test_profile": "SICKLING TEST", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000402", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.647734", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "SICK", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000402", "test_done_on": "all", "test_name": "SICKLING TEST", "updated_at": "2025-07-09T12:20:55.647737"}}, {"id": 1752821808910, "test_id": 85, "test_master_id": 85, "testName": "BENCE JONES PROTEIN-URINE", "test_name": "BENCE JONES PROTEIN-URINE", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "CLINICAL_PATHOLOGY", "hms_code": "000316", "display_name": "BENCE JONES PROTEIN-URINE", "short_name": "BJP", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "NEGATIVE", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 85, "testName": "BENCE JONES PROTEIN-URINE", "test_profile": "BENCE JONES PROTEIN-URINE", "test_price": 100, "department": "CLINICAL_PATHOLOGY", "hmsCode": "000316", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NEGATIVE", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.740444", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": "NEGATIVE", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "BJP", "source_sheet": "Clinical Pathology", "specimen_code": null, "test_code": "000316", "test_done_on": "all", "test_name": "BENCE JONES PROTEIN-URINE", "updated_at": "2025-07-09T12:20:55.740447"}}], "bill_amount": 200, "other_charges": 0, "discount_percent": 0, "subtotal": 200, "discount": 0, "gst_rate": 18, "gst_amount": 36, "tax": 36, "total_amount": 200, "paid_amount": 200, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-18", "due_date": "2025-08-17", "notes": "", "branch": 1, "created_at": "2025-07-18T12:27:23.242000", "updated_at": "2025-07-18T12:27:23.242001", "tenant_id": 1, "created_by": 4}, {"id": 63, "invoice_number": "INV00063", "sid_number": "253", "patient_id": 51, "items": [{"amount": 3600, "applicable_to": "Both", "container": "<PERSON><PERSON> Container", "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 2, "department": "HAEMATOLOGY", "display_name": "DCP- Decarboxy Prothrombin PIVKA II", "hms_code": "001599", "id": 1752833748877, "instructions": "", "international_code": "", "interpretation": "", "method": "CMIA", "min_sample_qty": "", "price": 3600, "primary_specimen": "SERUM", "quantity": 1, "reference_range": "17.36 - 50.90", "reporting_days": 10, "result_unit": "mAU/ml", "service_time": "", "short_name": "DCP", "specimen": "SERUM", "suffix_desc": "", "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_done_on": "all", "test_id": 17, "test_master_data": {"applicableTo": "Both", "applicable_to": "Both", "container": "<PERSON><PERSON> Container", "container_code": 12, "created_at": "2025-07-09T12:20:55.640014", "criticalHigh": null, "criticalLow": null, "critical_high": null, "critical_low": null, "cutoffTime": "", "decimals": 2, "department": "HAEMATOLOGY", "excel_source": true, "hmsCode": "001599", "id": 17, "instructions": null, "isActive": true, "is_active": true, "method": "CMIA", "method_code": 29, "minSampleQty": "", "min_sample_qty": null, "notes": null, "price": 3600, "referenceRange": "17.36 - 50.90", "reference_range": "17.36 - 50.90", "reportingDays": 10, "reporting_days": 10, "resultUnit": "mAU/ml", "result_type": "-", "result_unit": "mAU/ml", "serviceTime": "", "short_name": "DCP", "source_sheet": "HAEMATOLOGY", "specimen": "SERUM", "specimen_code": 39, "testDoneOn": "all", "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_code": "001599", "test_done_on": "all", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "test_price": 3600, "test_profile": "DCP- Decarboxy Prothrombin PIVKA II", "updated_at": "2025-07-09T12:20:55.640017"}, "test_master_id": 17, "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "test_price": 3600, "test_suffix": "", "unacceptable_conditions": ""}, {"amount": 1000, "applicable_to": "Both", "container": "<PERSON><PERSON><PERSON>", "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 1, "department": "HAEMATOLOGY", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "hms_code": "000372", "id": 1752833755080, "instructions": "", "international_code": "", "interpretation": "", "method": "Spectrophotometry", "min_sample_qty": "", "price": 1000, "primary_specimen": "HEPARIN BLOOD", "quantity": 1, "reference_range": "Less than 1.5", "reporting_days": 0, "result_unit": "%", "service_time": "", "short_name": "Met<PERSON>-<PERSON><PERSON><PERSON>", "specimen": "HEPARIN BLOOD", "suffix_desc": "", "testName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "test_done_on": "all", "test_id": 44, "test_master_data": {"applicableTo": "Both", "applicable_to": "Both", "container": "<PERSON><PERSON><PERSON>", "container_code": 9, "created_at": "2025-07-09T12:20:55.644222", "criticalHigh": null, "criticalLow": null, "critical_high": null, "critical_low": null, "cutoffTime": "", "decimals": 1, "department": "HAEMATOLOGY", "excel_source": true, "hmsCode": "000372", "id": 44, "instructions": null, "isActive": true, "is_active": true, "method": "Spectrophotometry", "method_code": 131, "minSampleQty": "", "min_sample_qty": null, "notes": null, "price": 1000, "referenceRange": "Less than 1.5", "reference_range": "Less than 1.5", "reportingDays": "", "reporting_days": 0, "resultUnit": "%", "result_type": "Numeric", "result_unit": "%", "serviceTime": "", "short_name": "Met<PERSON>-<PERSON><PERSON><PERSON>", "source_sheet": "HAEMATOLOGY", "specimen": "HEPARIN BLOOD", "specimen_code": 25, "testDoneOn": "all", "testName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "test_code": "000372", "test_done_on": "all", "test_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "test_price": 1000, "test_profile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "updated_at": "2025-07-09T12:20:55.644225"}, "test_master_id": 44, "test_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "test_price": 1000, "test_suffix": "", "unacceptable_conditions": ""}], "bill_amount": 4600, "other_charges": 0, "discount_percent": 0, "subtotal": 4600, "discount": 0, "gst_rate": 18, "gst_amount": 828, "tax": 828, "total_amount": 4600, "paid_amount": 5000, "balance": -400, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-18", "due_date": "2025-08-17", "notes": "", "branch": 1, "created_at": "2025-07-18T15:46:23.662750", "updated_at": "2025-07-18T15:46:23.662752", "tenant_id": 1, "created_by": 4, "patient": {"first_name": "soo", "id": 51, "last_name": "R"}}, {"id": 64, "invoice_number": "INV00064", "sid_number": "344", "patient_id": 51, "items": [{"id": 1753096714884, "test_id": 9, "test_master_id": 9, "testName": "Anti Platelet Antibody", "test_name": "Anti Platelet Antibody", "amount": 7500, "price": 7500, "test_price": 7500, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000333", "display_name": "Anti Platelet Antibody", "short_name": "Anti Plate", "international_code": "", "method": "IF", "primary_specimen": "Serum", "specimen": "Serum", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 9, "testName": "Anti Platelet Antibody", "test_profile": "Anti Platelet Antibody", "test_price": 7500, "department": "HAEMATOLOGY", "hmsCode": "000333", "specimen": "Serum", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IF", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639147", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 83, "min_sample_qty": null, "price": 7500, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "Anti Plate", "source_sheet": "HAEMATOLOGY", "specimen_code": 64, "test_code": "000333", "test_done_on": "all", "test_name": "Anti Platelet Antibody", "updated_at": "2025-07-09T12:20:55.639149"}}, {"id": 1753096719631, "test_id": 10, "test_master_id": 10, "testName": "BLEEDING TIME", "test_name": "BLEEDING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000342", "display_name": "BLEEDING TIME", "short_name": "BT", "international_code": "", "method": "IVY", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "1 to 6", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 10, "testName": "BLEEDING TIME", "test_profile": "BLEEDING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000342", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "1 to 6", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IVY", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639261", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 100, "min_sample_qty": null, "price": 20, "reference_range": "1 to 6", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": "BT", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000342", "test_done_on": "all", "test_name": "BLEEDING TIME", "updated_at": "2025-07-09T12:20:55.639264"}}, {"id": 1753096738819, "test_id": 169, "test_master_id": 169, "testName": "Allergen - Milk", "test_name": "Allergen - Milk", "amount": 1000, "price": 1000, "test_price": 1000, "quantity": 1, "department": "SEROLOGY", "hms_code": "001287", "display_name": "Allergen - Milk", "short_name": "", "international_code": "", "method": "ImmunoCAP (Fluoroenzymeimmunoassay)- Specific IgE", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "", "reference_range": "Negative: < 0.1 Positive: >= 0.1", "result_unit": "kUA/L", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 169, "testName": "Allergen - Milk", "test_profile": "Allergen - Milk", "test_price": 1000, "department": "SEROLOGY", "hmsCode": "001287", "specimen": "SERUM", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Negative: < 0.1 Positive: >= 0.1", "resultUnit": "kUA/L", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "ImmunoCAP (Fluoroenzymeimmunoassay)- Specific IgE", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:56.160788", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 182, "min_sample_qty": null, "price": 1000, "reference_range": "Negative: < 0.1 Positive: >= 0.1", "reporting_days": 0, "result_type": "Pick List", "result_unit": "kUA/L", "short_name": null, "source_sheet": "Serology", "specimen_code": 40, "test_code": "001287", "test_done_on": "all", "test_name": "Allergen - Milk", "updated_at": "2025-07-09T12:20:56.160792"}}, {"id": 1753096840697, "test_id": 49, "test_master_id": 49, "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_name": "NASAL SMEAR FOR EOSINOPHILS", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000425", "display_name": "NASAL SMEAR FOR EOSINOPHILS", "short_name": "", "international_code": "", "method": "", "primary_specimen": "NASAL SMEAR", "specimen": "NASAL SMEAR", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 49, "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_profile": "NASAL SMEAR FOR EOSINOPHILS", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000425", "specimen": "NASAL SMEAR", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644783", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": 28, "test_code": "000425", "test_done_on": "all", "test_name": "NASAL SMEAR FOR EOSINOPHILS", "updated_at": "2025-07-09T12:20:55.644786"}}, {"id": 1753096854778, "test_id": 48, "test_master_id": 48, "testName": "MYOGLOBIN-SERUM", "test_name": "MYOGLOBIN-SERUM", "amount": 1200, "price": 1200, "test_price": 1200, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000426", "display_name": "MYOGLOBIN-SERUM", "short_name": "MYOS", "international_code": "", "method": "ECLIA", "primary_specimen": "Serum", "specimen": "Serum", "container": "", "reference_range": "25-58", "result_unit": "ug/L", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 48, "testName": "MYOGLOBIN-SERUM", "test_profile": "MYOGLOBIN-SERUM", "test_price": 1200, "department": "HAEMATOLOGY", "hmsCode": "000426", "specimen": "Serum", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "25-58", "resultUnit": "ug/L", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "ECLIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644672", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 52, "min_sample_qty": null, "price": 1200, "reference_range": "25-58", "reporting_days": 0, "result_type": "Numeric", "result_unit": "ug/L", "short_name": "MYOS", "source_sheet": "HAEMATOLOGY", "specimen_code": 64, "test_code": "000426", "test_done_on": "all", "test_name": "MYOGLOBIN-SERUM", "updated_at": "2025-07-09T12:20:55.644675"}}, {"id": 1753096950259, "test_id": 76, "test_master_id": 76, "testName": "SICKLING TEST", "test_name": "SICKLING TEST", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000402", "display_name": "SICKLING TEST", "short_name": "SICK", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 76, "testName": "SICKLING TEST", "test_profile": "SICKLING TEST", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000402", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.647734", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "SICK", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000402", "test_done_on": "all", "test_name": "SICKLING TEST", "updated_at": "2025-07-09T12:20:55.647737"}}, {"id": 1753096956427, "test_id": 47, "test_master_id": 47, "testName": "MPV", "test_name": "MPV", "amount": 0, "price": 0, "test_price": 0, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001640", "display_name": "MPV", "short_name": "", "international_code": "", "method": "", "primary_specimen": "BLOOD", "specimen": "BLOOD", "container": "EDTA Container", "reference_range": "", "result_unit": "fl", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 47, "testName": "MPV", "test_profile": "MPV", "test_price": 0, "department": "HAEMATOLOGY", "hmsCode": "001640", "specimen": "BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "fl", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.644558", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 0, "reference_range": null, "reporting_days": 0, "result_type": "Numeric", "result_unit": "fl", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": 9, "test_code": "001640", "test_done_on": "all", "test_name": "MPV", "updated_at": "2025-07-09T12:20:55.644561"}}], "bill_amount": 9920, "other_charges": null, "discount_percent": 0, "subtotal": 9920, "discount": 0, "gst_rate": 18, "gst_amount": 1785.6, "tax": 1785.6, "total_amount": 9920, "paid_amount": 10000, "balance": -80, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-21", "due_date": "2025-08-20", "notes": "", "branch": 1, "created_at": "2025-07-21T17:03:48.312532", "updated_at": "2025-07-21T17:03:48.312533", "tenant_id": 1, "created_by": 4}, {"id": 65, "invoice_number": "INV00065", "sid_number": "346", "patient_id": 64, "items": [{"id": 1753165597491, "test_id": 17, "test_master_id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "amount": 3600, "price": 3600, "test_price": 3600, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001599", "display_name": "DCP- Decarboxy Prothrombin PIVKA II", "short_name": "DCP", "international_code": "", "method": "CMIA", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "reference_range": "17.36 - 50.90", "result_unit": "mAU/ml", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 10, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_profile": "DCP- Decarboxy Prothrombin PIVKA II", "test_price": 3600, "department": "HAEMATOLOGY", "hmsCode": "001599", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "serviceTime": "", "reportingDays": 10, "cutoffTime": "", "referenceRange": "17.36 - 50.90", "resultUnit": "mAU/ml", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "CMIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 12, "created_at": "2025-07-09T12:20:55.640014", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 29, "min_sample_qty": null, "price": 3600, "reference_range": "17.36 - 50.90", "reporting_days": 10, "result_type": "-", "result_unit": "mAU/ml", "short_name": "DCP", "source_sheet": "HAEMATOLOGY", "specimen_code": 39, "test_code": "001599", "test_done_on": "all", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "updated_at": "2025-07-09T12:20:55.640017"}}, {"id": 1753165604814, "test_id": 13, "test_master_id": 13, "testName": "CLOTTING TIME", "test_name": "CLOTTING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000347", "display_name": "CLOTTING TIME", "short_name": "", "international_code": "", "method": "<PERSON> and <PERSON> method", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "5 - 10", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 13, "testName": "CLOTTING TIME", "test_profile": "CLOTTING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000347", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "5 - 10", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "<PERSON> and <PERSON> method", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639584", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 106, "min_sample_qty": null, "price": 20, "reference_range": "5 - 10", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000347", "test_done_on": "all", "test_name": "CLOTTING TIME", "updated_at": "2025-07-09T12:20:55.639586"}}, {"id": 1753165649405, "test_id": 588, "test_master_id": 588, "testName": "Urea", "test_name": "Urea", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "BIOCHEMISTRY", "hms_code": "000214", "display_name": "Urea", "short_name": "<PERSON><PERSON>", "international_code": "", "method": "Urease/GLDH", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "", "result_unit": "mg/dL", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 588, "testName": "Urea", "test_profile": "Urea", "test_price": 100, "department": "BIOCHEMISTRY", "hmsCode": "000214", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "mg/dL", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "Urease/GLDH", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.555920", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 165, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Numeric", "result_unit": "mg/dL", "short_name": "<PERSON><PERSON>", "source_sheet": "BioChemistry", "specimen_code": 64, "test_code": "000214", "test_done_on": "all", "test_name": "Urea", "updated_at": "2025-07-09T12:20:56.555922"}}], "bill_amount": 3720, "other_charges": 0, "discount_percent": 0, "subtotal": 3720, "discount": 0, "gst_rate": 18, "gst_amount": 669.6, "tax": 669.6, "total_amount": 3720, "paid_amount": 4000, "balance": -280, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-22", "due_date": "2025-08-21", "notes": "", "branch": 1, "created_at": "2025-07-22T11:58:08.452979", "updated_at": "2025-07-22T11:58:08.452980", "tenant_id": 1, "created_by": 4}, {"id": 66, "invoice_number": "INV00066", "sid_number": "357", "patient_id": 65, "items": [{"id": 1753168117381, "test_id": 499, "test_master_id": 499, "testName": "Glucose, Fasting", "test_name": "Glucose, Fasting", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "BIOCHEMISTRY", "hms_code": "000075", "display_name": "Glucose, Fasting", "short_name": "FBS", "international_code": "", "method": "Colorimetric : GOD-POD", "primary_specimen": "Fluoride", "specimen": "Fluoride", "container": "<PERSON>", "reference_range": "", "result_unit": "mg/dL", "decimals": 1, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "Samples to be collected between 10 to 12 hrs of Fasting in Fluoride Tube", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 499, "testName": "Glucose, Fasting", "test_profile": "Glucose, Fasting", "test_price": 20, "department": "BIOCHEMISTRY", "hmsCode": "000075", "specimen": "Fluoride", "container": "<PERSON>", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "mg/dL", "decimals": 1, "criticalLow": null, "criticalHigh": null, "method": "Colorimetric : GOD-POD", "instructions": "Samples to be collected between 10 to 12 hrs of Fasting in Fluoride Tube", "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 10, "created_at": "2025-07-09T12:20:56.545310", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 37, "min_sample_qty": null, "price": 20, "reference_range": null, "reporting_days": 0, "result_type": "-", "result_unit": "mg/dL", "short_name": "FBS", "source_sheet": "BioChemistry", "specimen_code": 24, "test_code": "000075", "test_done_on": "all", "test_name": "Glucose, Fasting", "updated_at": "2025-07-09T12:20:56.545313"}}, {"id": 1753168178902, "test_id": 503, "test_master_id": 503, "testName": "<PERSON>lu<PERSON>e, Post-prandial", "test_name": "<PERSON>lu<PERSON>e, Post-prandial", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "BIOCHEMISTRY", "hms_code": "000123", "display_name": "<PERSON>lu<PERSON>e, Post-prandial", "short_name": "PPBS", "international_code": "", "method": "Colorimetric : GOD-POD", "primary_specimen": "Fluoride", "specimen": "Fluoride", "container": "<PERSON>", "reference_range": "", "result_unit": "mg/dL", "decimals": 1, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 503, "testName": "<PERSON>lu<PERSON>e, Post-prandial", "test_profile": "<PERSON>lu<PERSON>e, Post-prandial", "test_price": 20, "department": "BIOCHEMISTRY", "hmsCode": "000123", "specimen": "Fluoride", "container": "<PERSON>", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "mg/dL", "decimals": 1, "criticalLow": null, "criticalHigh": null, "method": "Colorimetric : GOD-POD", "instructions": null, "notes": "Blood glucose level primarily depends upon individual characters like type and quantity of food intake, physical activity and the body’s metabolic response.  Lower postprandial blood glucose level than fasting level can be noticed in variety of conditions in both normal population and diabetics. Various modifiable factors along with underlying condition of patient that affect blood glucose levels are:\n\n1. Pre-analytical factors such as smoking, caffeinated drinks, use of hypoglycemic drugs, heavy exercise, anxiety, strenuous activity before sampling & time of sample collection. 2.Change in glucagon to insulin ratio, the commonest cause of impaired fasting glucose tolerance and diabetes mellitus. 3. high carbohydrate meal at bedtime or not enough diabetic medication, disturbed sleep, and other lesser known entities like Dawn phenomenon and Somogyi effect. 4.Chewing and eating slower or gastroparesis can reduce the reactive glucose surge post meal. 5.Consumption of less or eat non-carbohydrate meal before testing for PPBG level.\n\nDue to individual variation of FBG and PPBG and large imprecision in analysis, researchers have advocated the use of HbA1c only for diabetes diagnosis.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 10, "created_at": "2025-07-09T12:20:56.545840", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 37, "min_sample_qty": null, "price": 20, "reference_range": null, "reporting_days": 0, "result_type": "-", "result_unit": "mg/dL", "short_name": "PPBS", "source_sheet": "BioChemistry", "specimen_code": 24, "test_code": "000123", "test_done_on": "all", "test_name": "<PERSON>lu<PERSON>e, Post-prandial", "updated_at": "2025-07-09T12:20:56.545853"}}], "bill_amount": 40, "other_charges": null, "discount_percent": 0, "subtotal": 40, "discount": 0, "gst_rate": 18, "gst_amount": 7.2, "tax": 7.2, "total_amount": 40, "paid_amount": 50, "balance": -10, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-22", "due_date": "2025-08-21", "notes": "", "branch": 1, "created_at": "2025-07-22T12:42:53.165966", "updated_at": "2025-07-22T12:42:53.165969", "tenant_id": 1, "created_by": 4}, {"id": 67, "invoice_number": "INV00067", "sid_number": "359", "patient_id": 51, "items": [{"id": 1753168644568, "test_id": 503, "test_master_id": 503, "testName": "<PERSON>lu<PERSON>e, Post-prandial", "test_name": "<PERSON>lu<PERSON>e, Post-prandial", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "BIOCHEMISTRY", "hms_code": "000123", "display_name": "<PERSON>lu<PERSON>e, Post-prandial", "short_name": "PPBS", "international_code": "", "method": "Colorimetric : GOD-POD", "primary_specimen": "Fluoride", "specimen": "Fluoride", "container": "<PERSON>", "reference_range": "", "result_unit": "mg/dL", "decimals": 1, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 503, "testName": "<PERSON>lu<PERSON>e, Post-prandial", "test_profile": "<PERSON>lu<PERSON>e, Post-prandial", "test_price": 20, "department": "BIOCHEMISTRY", "hmsCode": "000123", "specimen": "Fluoride", "container": "<PERSON>", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "mg/dL", "decimals": 1, "criticalLow": null, "criticalHigh": null, "method": "Colorimetric : GOD-POD", "instructions": null, "notes": "Blood glucose level primarily depends upon individual characters like type and quantity of food intake, physical activity and the body’s metabolic response.  Lower postprandial blood glucose level than fasting level can be noticed in variety of conditions in both normal population and diabetics. Various modifiable factors along with underlying condition of patient that affect blood glucose levels are:\n\n1. Pre-analytical factors such as smoking, caffeinated drinks, use of hypoglycemic drugs, heavy exercise, anxiety, strenuous activity before sampling & time of sample collection. 2.Change in glucagon to insulin ratio, the commonest cause of impaired fasting glucose tolerance and diabetes mellitus. 3. high carbohydrate meal at bedtime or not enough diabetic medication, disturbed sleep, and other lesser known entities like Dawn phenomenon and Somogyi effect. 4.Chewing and eating slower or gastroparesis can reduce the reactive glucose surge post meal. 5.Consumption of less or eat non-carbohydrate meal before testing for PPBG level.\n\nDue to individual variation of FBG and PPBG and large imprecision in analysis, researchers have advocated the use of HbA1c only for diabetes diagnosis.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 10, "created_at": "2025-07-09T12:20:56.545840", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 37, "min_sample_qty": null, "price": 20, "reference_range": null, "reporting_days": 0, "result_type": "-", "result_unit": "mg/dL", "short_name": "PPBS", "source_sheet": "BioChemistry", "specimen_code": 24, "test_code": "000123", "test_done_on": "all", "test_name": "<PERSON>lu<PERSON>e, Post-prandial", "updated_at": "2025-07-09T12:20:56.545853"}}], "bill_amount": 20, "other_charges": 0, "discount_percent": 0, "subtotal": 20, "discount": 0, "gst_rate": 18, "gst_amount": 3.6, "tax": 3.6, "total_amount": 20, "paid_amount": 40, "balance": -20, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-07-22", "due_date": "2025-08-21", "notes": "", "branch": 1, "created_at": "2025-07-22T12:47:47.017426", "updated_at": "2025-07-22T12:47:47.017427", "tenant_id": 1, "created_by": 4}, {"id": 68, "invoice_number": "INV00068", "sid_number": "362", "patient_id": 66, "items": [{"id": 1754051575189, "test_id": 10, "test_master_id": 10, "testName": "BLEEDING TIME", "test_name": "BLEEDING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000342", "display_name": "BLEEDING TIME", "short_name": "BT", "international_code": "", "method": "IVY", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "1 to 6", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 10, "testName": "BLEEDING TIME", "test_profile": "BLEEDING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000342", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "1 to 6", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IVY", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639261", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 100, "min_sample_qty": null, "price": 20, "reference_range": "1 to 6", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": "BT", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000342", "test_done_on": "all", "test_name": "BLEEDING TIME", "updated_at": "2025-07-09T12:20:55.639264"}}], "bill_amount": 20, "other_charges": 0, "discount_percent": 0, "subtotal": 20, "discount": 0, "gst_rate": 18, "gst_amount": 3.6, "tax": 3.6, "total_amount": 20, "paid_amount": 20, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-01", "due_date": "2025-08-31", "notes": "", "branch": 1, "created_at": "2025-08-01T18:03:07.416509", "updated_at": "2025-08-01T18:03:07.416511", "tenant_id": 1, "created_by": 4}, {"id": 69, "invoice_number": "INV00069", "sid_number": "364", "patient_id": 67, "items": [{"id": 1754054785186, "test_id": 14, "test_master_id": 14, "testName": "COLD AGGLUTININ", "test_name": "COLD AGGLUTININ", "amount": 600, "price": 600, "test_price": 600, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000348", "display_name": "COLD AGGLUTININ", "short_name": "", "international_code": "", "method": "", "primary_specimen": "Serum", "specimen": "Serum", "container": "", "reference_range": "NEGATIVE UPTO 1:32", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 14, "testName": "COLD AGGLUTININ", "test_profile": "COLD AGGLUTININ", "test_price": 600, "department": "HAEMATOLOGY", "hmsCode": "000348", "specimen": "Serum", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NEGATIVE UPTO 1:32", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": "Titers of 1:32 or higher are considered elevated by this technique.Elevated titers are seen in primary atypical pneumonia and in certain hemolytic anemias.Primary atypical pneumonia can be caused by Mycoplasma pneumoniae, influenza A, influenza B,parainfluenza, and adenoviruses.However, a fourfold rise in the cold agglutinins usually begins to appear late in the first week or during the second week of the disease and begins to decrease between the fourth and sixth weeksLow titers of cold agglutinins have been demonstrated in malaria, peripheral vascular disease, and common respiratory disease.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639687", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 600, "reference_range": "NEGATIVE UPTO 1:32", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": 64, "test_code": "000348", "test_done_on": "all", "test_name": "COLD AGGLUTININ", "updated_at": "2025-07-09T12:20:55.639690"}}, {"id": 1754054793096, "test_id": 369, "test_master_id": 369, "testName": "Tissue Transglutaminase (tTG) Ab-IgG", "test_name": "Tissue Transglutaminase (tTG) Ab-IgG", "amount": 1500, "price": 1500, "test_price": 1500, "quantity": 1, "department": "SEROLOGY", "hms_code": "001557", "display_name": "Tissue Transglutaminase (tTG) Ab-IgG", "short_name": "TTgG", "international_code": "", "method": "EIA", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "Serum Container", "reference_range": "Less than 4.0 : Negative 4.0-10.0      : Weak positive More than 10.0: Positive", "result_unit": "u/mL", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 369, "testName": "Tissue Transglutaminase (tTG) Ab-IgG", "test_profile": "Tissue Transglutaminase (tTG) Ab-IgG", "test_price": 1500, "department": "SEROLOGY", "hmsCode": "001557", "specimen": "SERUM", "container": "Serum Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Less than 4.0 : Negative 4.0-10.0      : Weak positive More than 10.0: Positive", "resultUnit": "u/mL", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "EIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 4, "created_at": "2025-07-09T12:20:56.186831", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 53, "min_sample_qty": null, "price": 1500, "reference_range": "Less than 4.0 : Negative 4.0-10.0      : Weak positive More than 10.0: Positive", "reporting_days": 0, "result_type": null, "result_unit": "u/mL", "short_name": "TTgG", "source_sheet": "Serology", "specimen_code": 39, "test_code": "001557", "test_done_on": "all", "test_name": "Tissue Transglutaminase (tTG) Ab-IgG", "updated_at": "2025-07-09T12:20:56.186833"}}, {"id": 1754054812638, "test_id": 49, "test_master_id": 49, "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_name": "NASAL SMEAR FOR EOSINOPHILS", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000425", "display_name": "NASAL SMEAR FOR EOSINOPHILS", "short_name": "", "international_code": "", "method": "", "primary_specimen": "NASAL SMEAR", "specimen": "NASAL SMEAR", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 49, "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_profile": "NASAL SMEAR FOR EOSINOPHILS", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000425", "specimen": "NASAL SMEAR", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644783", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": 28, "test_code": "000425", "test_done_on": "all", "test_name": "NASAL SMEAR FOR EOSINOPHILS", "updated_at": "2025-07-09T12:20:55.644786"}}, {"id": 1754054821059, "test_id": 48, "test_master_id": 48, "testName": "MYOGLOBIN-SERUM", "test_name": "MYOGLOBIN-SERUM", "amount": 1200, "price": 1200, "test_price": 1200, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000426", "display_name": "MYOGLOBIN-SERUM", "short_name": "MYOS", "international_code": "", "method": "ECLIA", "primary_specimen": "Serum", "specimen": "Serum", "container": "", "reference_range": "25-58", "result_unit": "ug/L", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 48, "testName": "MYOGLOBIN-SERUM", "test_profile": "MYOGLOBIN-SERUM", "test_price": 1200, "department": "HAEMATOLOGY", "hmsCode": "000426", "specimen": "Serum", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "25-58", "resultUnit": "ug/L", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "ECLIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644672", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 52, "min_sample_qty": null, "price": 1200, "reference_range": "25-58", "reporting_days": 0, "result_type": "Numeric", "result_unit": "ug/L", "short_name": "MYOS", "source_sheet": "HAEMATOLOGY", "specimen_code": 64, "test_code": "000426", "test_done_on": "all", "test_name": "MYOGLOBIN-SERUM", "updated_at": "2025-07-09T12:20:55.644675"}}, {"id": 1754054833859, "test_id": 139, "test_master_id": 139, "testName": "JAPANESE ENCEPHALITIS VIRUS DETECTION, PCR", "test_name": "JAPANESE ENCEPHALITIS VIRUS DETECTION, PCR", "amount": 5000, "price": 5000, "test_price": 5000, "quantity": 1, "department": "MOLECULAR_BIOLOGY", "hms_code": "001144", "display_name": "JAPANESE ENCEPHALITIS VIRUS DETECTION, PCR", "short_name": "", "international_code": "", "method": "RT PCR", "primary_specimen": "Serum/CSF", "specimen": "Serum/CSF", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 139, "testName": "JAPANESE ENCEPHALITIS VIRUS DETECTION, PCR", "test_profile": "JAPANESE ENCEPHALITIS VIRUS DETECTION, PCR", "test_price": 5000, "department": "MOLECULAR_BIOLOGY", "hmsCode": "001144", "specimen": "Serum/CSF", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "RT PCR", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.835908", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 128, "min_sample_qty": null, "price": 5000, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": null, "source_sheet": "Molecular Biology", "specimen_code": 48, "test_code": "001144", "test_done_on": "all", "test_name": "JAPANESE ENCEPHALITIS VIRUS DETECTION, PCR", "updated_at": "2025-07-09T12:20:55.835911"}}], "bill_amount": 8400, "other_charges": 0, "discount_percent": 0, "subtotal": 8400, "discount": 0, "gst_rate": 18, "gst_amount": 1512, "tax": 1512, "total_amount": 8400, "paid_amount": 9000, "balance": -600, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-01", "due_date": "2025-08-31", "notes": "", "branch": 1, "created_at": "2025-08-01T18:57:39.389151", "updated_at": "2025-08-01T18:57:39.389153", "tenant_id": 1, "created_by": 4}, {"id": 70, "invoice_number": "INV00070", "sid_number": "367", "patient_id": 69, "items": [{"id": 1754471178939, "test_id": 17, "test_master_id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "amount": 3600, "price": 3600, "test_price": 3600, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "001599", "display_name": "DCP- Decarboxy Prothrombin PIVKA II", "short_name": "DCP", "international_code": "", "method": "CMIA", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "reference_range": "17.36 - 50.90", "result_unit": "mAU/ml", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 10, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 17, "testName": "DCP- Decarboxy Prothrombin PIVKA II", "test_profile": "DCP- Decarboxy Prothrombin PIVKA II", "test_price": 3600, "department": "HAEMATOLOGY", "hmsCode": "001599", "specimen": "SERUM", "container": "<PERSON><PERSON> Container", "serviceTime": "", "reportingDays": 10, "cutoffTime": "", "referenceRange": "17.36 - 50.90", "resultUnit": "mAU/ml", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "CMIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 12, "created_at": "2025-07-09T12:20:55.640014", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 29, "min_sample_qty": null, "price": 3600, "reference_range": "17.36 - 50.90", "reporting_days": 10, "result_type": "-", "result_unit": "mAU/ml", "short_name": "DCP", "source_sheet": "HAEMATOLOGY", "specimen_code": 39, "test_code": "001599", "test_done_on": "all", "test_name": "DCP- Decarboxy Prothrombin PIVKA II", "updated_at": "2025-07-09T12:20:55.640017"}}, {"id": 1754471196749, "test_id": 48, "test_master_id": 48, "testName": "MYOGLOBIN-SERUM", "test_name": "MYOGLOBIN-SERUM", "amount": 1200, "price": 1200, "test_price": 1200, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000426", "display_name": "MYOGLOBIN-SERUM", "short_name": "MYOS", "international_code": "", "method": "ECLIA", "primary_specimen": "Serum", "specimen": "Serum", "container": "", "reference_range": "25-58", "result_unit": "ug/L", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 48, "testName": "MYOGLOBIN-SERUM", "test_profile": "MYOGLOBIN-SERUM", "test_price": 1200, "department": "HAEMATOLOGY", "hmsCode": "000426", "specimen": "Serum", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "25-58", "resultUnit": "ug/L", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "ECLIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644672", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 52, "min_sample_qty": null, "price": 1200, "reference_range": "25-58", "reporting_days": 0, "result_type": "Numeric", "result_unit": "ug/L", "short_name": "MYOS", "source_sheet": "HAEMATOLOGY", "specimen_code": 64, "test_code": "000426", "test_done_on": "all", "test_name": "MYOGLOBIN-SERUM", "updated_at": "2025-07-09T12:20:55.644675"}}, {"id": 1754471209697, "test_id": 506, "test_master_id": 506, "testName": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "test_name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "BIOCHEMISTRY", "hms_code": "000241", "display_name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "short_name": "RBS", "international_code": "", "method": "Colorimetric : GOD-POD", "primary_specimen": "Fluoride", "specimen": "Fluoride", "container": "<PERSON>", "reference_range": "", "result_unit": "mg/dL", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "expect sunday", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 506, "testName": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "test_profile": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "test_price": 20, "department": "BIOCHEMISTRY", "hmsCode": "000241", "specimen": "Fluoride", "container": "<PERSON>", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "mg/dL", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "Colorimetric : GOD-POD", "instructions": null, "notes": "Blood glucose level primarily depends upon individual characters like type and quantity of food intake, physical activity and the body’s metabolic response.  Lower postprandial blood glucose level than fasting level can be noticed in variety of conditions in both normal population and diabetics. Various modifiable factors along with underlying condition of patient that affect blood glucose levels are:\n\n1. Pre-analytical factors such as smoking, caffeinated drinks, use of hypoglycemic drugs, heavy exercise, anxiety, strenuous activity before sampling & time of sample collection. 2.Change in glucagon to insulin ratio, the commonest cause of impaired fasting glucose tolerance and diabetes mellitus. 3. high carbohydrate meal at bedtime or not enough diabetic medication, disturbed sleep, and other lesser known entities like Dawn phenomenon and Somogyi effect. 4.Chewing and eating slower or gastroparesis can reduce the reactive glucose surge post meal. 5.Consumption of less or eat non-carbohydrate meal before testing for PPBG level.\n\nDue to individual variation of FBG and PPBG and large imprecision in analysis, researchers have advocated the use of HbA1c only for diabetes diagnosis.", "minSampleQty": "", "testDoneOn": "expect sunday", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 10, "created_at": "2025-07-09T12:20:56.546270", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 37, "min_sample_qty": null, "price": 20, "reference_range": null, "reporting_days": 0, "result_type": "-", "result_unit": "mg/dL", "short_name": "RBS", "source_sheet": "BioChemistry", "specimen_code": 24, "test_code": "000241", "test_done_on": "expect sunday", "test_name": "<PERSON><PERSON><PERSON><PERSON>, <PERSON>", "updated_at": "2025-07-09T12:20:56.546272"}}], "bill_amount": 4820, "other_charges": 0, "discount_percent": 0, "subtotal": 4820, "discount": 0, "gst_rate": 18, "gst_amount": 867.6, "tax": 867.6, "total_amount": 4820, "paid_amount": 5000, "balance": -180, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-06", "due_date": "2025-09-05", "notes": "", "branch": 1, "created_at": "2025-08-06T14:38:11.717346", "updated_at": "2025-08-06T14:38:11.717348", "tenant_id": 1, "created_by": 4}, {"id": 71, "invoice_number": "INV00071", "sid_number": "375", "patient_id": 70, "items": [{"id": 1754644716857, "test_id": 42, "test_master_id": 42, "testName": "MCHC", "test_name": "MCHC", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000370", "display_name": "MCHC", "short_name": "MCHC", "international_code": "", "method": "Calculated", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "EDTA Container", "reference_range": "", "result_unit": "g/L", "decimals": 1, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 42, "testName": "MCHC", "test_profile": "MCHC", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000370", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "g/L", "decimals": 1, "criticalLow": null, "criticalHigh": null, "method": "Calculated", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.643995", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 20, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Numeric", "result_unit": "g/L", "short_name": "MCHC", "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "000370", "test_done_on": "all", "test_name": "MCHC", "updated_at": "2025-07-09T12:20:55.643998"}}, {"id": 1754644722920, "test_id": 44, "test_master_id": 44, "testName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "test_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "amount": 1000, "price": 1000, "test_price": 1000, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000372", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "short_name": "Met<PERSON>-<PERSON><PERSON><PERSON>", "international_code": "", "method": "Spectrophotometry", "primary_specimen": "HEPARIN BLOOD", "specimen": "HEPARIN BLOOD", "container": "<PERSON><PERSON><PERSON>", "reference_range": "Less than 1.5", "result_unit": "%", "decimals": 1, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 44, "testName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "test_profile": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "test_price": 1000, "department": "HAEMATOLOGY", "hmsCode": "000372", "specimen": "HEPARIN BLOOD", "container": "<PERSON><PERSON><PERSON>", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Less than 1.5", "resultUnit": "%", "decimals": 1, "criticalLow": null, "criticalHigh": null, "method": "Spectrophotometry", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 9, "created_at": "2025-07-09T12:20:55.644222", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 131, "min_sample_qty": null, "price": 1000, "reference_range": "Less than 1.5", "reporting_days": 0, "result_type": "Numeric", "result_unit": "%", "short_name": "Met<PERSON>-<PERSON><PERSON><PERSON>", "source_sheet": "HAEMATOLOGY", "specimen_code": 25, "test_code": "000372", "test_done_on": "all", "test_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Blood", "updated_at": "2025-07-09T12:20:55.644225"}}], "bill_amount": 1100, "other_charges": 0, "discount_percent": 0, "subtotal": 1100, "discount": 0, "gst_rate": 18, "gst_amount": 198, "tax": 198, "total_amount": 1100, "paid_amount": 1200, "balance": -100, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "notes": "", "branch": 1, "created_at": "2025-08-08T14:49:08.846206", "updated_at": "2025-08-08T14:49:08.846210", "tenant_id": 1, "created_by": 4}, {"id": 72, "invoice_number": "INV00072", "sid_number": "383", "patient_id": 69, "items": [{"id": 1754645317870, "test_id": 46, "test_master_id": 46, "testName": "MICROFILARIA (MF)  by QBC", "test_name": "MICROFILARIA (MF)  by QBC", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000431", "display_name": "MICROFILARIA (MF)  by QBC", "short_name": "QBC", "international_code": "", "method": "QBC", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "", "reference_range": "( QBC )", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 46, "testName": "MICROFILARIA (MF)  by QBC", "test_profile": "MICROFILARIA (MF)  by QBC", "test_price": 400, "department": "HAEMATOLOGY", "hmsCode": "000431", "specimen": "EDTA BLOOD", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "( QBC )", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "QBC", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644445", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 122, "min_sample_qty": null, "price": 400, "reference_range": "( QBC )", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "QBC", "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "000431", "test_done_on": "all", "test_name": "MICROFILARIA (MF)  by QBC", "updated_at": "2025-07-09T12:20:55.644448"}}, {"id": 1754645325891, "test_id": 26, "test_master_id": 26, "testName": "FILARIAL ANTIGEN", "test_name": "FILARIAL ANTIGEN", "amount": 700, "price": 700, "test_price": 700, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000375", "display_name": "FILARIAL ANTIGEN", "short_name": "MFAG", "international_code": "", "method": "IC", "primary_specimen": "", "specimen": "", "container": "EDTA Container", "reference_range": "NEGATIVE  The Test is structured to indicate the presence or absence of W.ban<PERSON> antigen in the sample.  The absence of antigen does not exclude Filariasis caused by other nematode species.", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 26, "testName": "FILARIAL ANTIGEN", "test_profile": "FILARIAL ANTIGEN", "test_price": 700, "department": "HAEMATOLOGY", "hmsCode": "000375", "specimen": null, "container": "EDTA Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NEGATIVE  The Test is structured to indicate the presence or absence of W.ban<PERSON> antigen in the sample.  The absence of antigen does not exclude Filariasis caused by other nematode species.", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IC", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.640968", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 80, "min_sample_qty": null, "price": 700, "reference_range": "NEGATIVE  The Test is structured to indicate the presence or absence of W.ban<PERSON> antigen in the sample.  The absence of antigen does not exclude Filariasis caused by other nematode species.", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "MFAG", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000375", "test_done_on": "all", "test_name": "FILARIAL ANTIGEN", "updated_at": "2025-07-09T12:20:55.640970"}}], "bill_amount": 1100, "other_charges": 0, "discount_percent": 0, "subtotal": 1100, "discount": 0, "gst_rate": 18, "gst_amount": 198, "tax": 198, "total_amount": 1100, "paid_amount": 1100, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "notes": "", "branch": 1, "created_at": "2025-08-08T15:01:38.433660", "updated_at": "2025-08-08T15:01:38.433664", "tenant_id": 1, "created_by": 4}, {"id": 73, "invoice_number": "INV00073", "sid_number": "387", "patient_id": 28, "items": [{"id": 1754645752947, "test_id": 10, "test_master_id": 10, "testName": "BLEEDING TIME", "test_name": "BLEEDING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000342", "display_name": "BLEEDING TIME", "short_name": "BT", "international_code": "", "method": "IVY", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "1 to 6", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 10, "testName": "BLEEDING TIME", "test_profile": "BLEEDING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000342", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "1 to 6", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IVY", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639261", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 100, "min_sample_qty": null, "price": 20, "reference_range": "1 to 6", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": "BT", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000342", "test_done_on": "all", "test_name": "BLEEDING TIME", "updated_at": "2025-07-09T12:20:55.639264"}}, {"amount": 1200, "description": "MYOGLOBIN-SERUM", "display_name": "MYOGLOBIN-SERUM", "id": 1754908340433, "price": 1200, "quantity": 1, "test_id": 48, "test_name": "MYOGLOBIN-SERUM", "unit_price": 1200, "total": 1200, "status": "Pending", "test_master_data": {"id": 48, "testName": "MYOGLOBIN-SERUM", "test_profile": "MYOGLOBIN-SERUM", "test_price": 1200, "department": "HAEMATOLOGY", "hmsCode": "000426", "specimen": "Serum", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "25-58", "resultUnit": "ug/L", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "ECLIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644672", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 52, "min_sample_qty": null, "price": 1200, "reference_range": "25-58", "reporting_days": 0, "result_type": "Numeric", "result_unit": "ug/L", "short_name": "MYOS", "source_sheet": "HAEMATOLOGY", "specimen_code": 64, "test_code": "000426", "test_done_on": "all", "test_name": "MYOGLOBIN-SERUM", "updated_at": "2025-07-09T12:20:55.644675"}}], "bill_amount": 20, "other_charges": 0, "discount_percent": 0, "subtotal": 20, "discount": 0, "gst_rate": 18, "gst_amount": 3.6, "tax": 3.6, "total_amount": 1200, "paid_amount": 20, "balance": 1396.0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "notes": "", "branch": 1, "created_at": "2025-08-08T15:06:14.763760", "updated_at": "2025-08-11T16:02:20.432620", "tenant_id": 1, "created_by": 4, "test_items": [{"id": 1, "name": "MYOGLOBIN-SERUM", "amount": 1200, "status": "Pending"}], "tax_amount": 216.0, "net_amount": 1416.0}, {"id": 74, "invoice_number": "INV00074", "sid_number": "389", "patient_id": 28, "items": [{"id": 1754647176627, "test_id": 8, "test_master_id": 8, "testName": "ABSOLUTE NEUTROPHIL COUNT", "test_name": "ABSOLUTE NEUTROPHIL COUNT", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000332", "display_name": "ABSOLUTE NEUTROPHIL COUNT", "short_name": "ANC", "international_code": "", "method": "Automated                        ", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "EDTA Container", "reference_range": "2000 - 7000", "result_unit": "cells/cumm", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 8, "testName": "ABSOLUTE NEUTROPHIL COUNT", "test_profile": "ABSOLUTE NEUTROPHIL COUNT", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000332", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "2000 - 7000", "resultUnit": "cells/cumm", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "Automated                        ", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.639034", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 10, "min_sample_qty": null, "price": 100, "reference_range": "2000 - 7000", "reporting_days": 0, "result_type": "-", "result_unit": "cells/cumm", "short_name": "ANC", "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "000332", "test_done_on": "all", "test_name": "ABSOLUTE NEUTROPHIL COUNT", "updated_at": "2025-07-09T12:20:55.639036"}}], "bill_amount": 100, "other_charges": 0, "discount_percent": 0, "subtotal": 100, "discount": 0, "gst_rate": 18, "gst_amount": 18, "tax": 18, "total_amount": 100, "paid_amount": 200, "balance": -100, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "notes": "", "branch": 1, "created_at": "2025-08-08T15:29:55.849737", "updated_at": "2025-08-08T15:29:55.849740", "tenant_id": 1, "created_by": 4}, {"id": 75, "invoice_number": "INV00075", "sid_number": "409", "patient_id": 71, "items": [{"id": 1754648286775, "test_id": 24, "test_master_id": 24, "testName": "FIBRINOGEN", "test_name": "FIBRINOGEN", "amount": 700, "price": 700, "test_price": 700, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000327", "display_name": "FIBRINOGEN", "short_name": "FIB", "international_code": "", "method": "COAGULATION", "primary_specimen": "Citrate Plasma", "specimen": "Citrate Plasma", "container": "Citrate Container", "reference_range": "200 - 400", "result_unit": "mg/dL", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 24, "testName": "FIBRINOGEN", "test_profile": "FIBRINOGEN", "test_price": 700, "department": "HAEMATOLOGY", "hmsCode": "000327", "specimen": "Citrate Plasma", "container": "Citrate Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "200 - 400", "resultUnit": "mg/dL", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "COAGULATION", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 8, "created_at": "2025-07-09T12:20:55.640759", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 30, "min_sample_qty": null, "price": 700, "reference_range": "200 - 400", "reporting_days": 0, "result_type": "Pick List", "result_unit": "mg/dL", "short_name": "FIB", "source_sheet": "HAEMATOLOGY", "specimen_code": 14, "test_code": "000327", "test_done_on": "all", "test_name": "FIBRINOGEN", "updated_at": "2025-07-09T12:20:55.640762"}}, {"amount": 400, "description": "Malarial Parasite (QBC)", "display_name": "Malarial Parasite (QBC)", "id": 1754741152271, "price": 400, "quantity": 1, "test_id": 40, "test_name": "Malarial Parasite (QBC)", "unit_price": 400, "total": 400, "status": "Pending", "test_master_data": {"id": 40, "testName": "Malarial Parasite (QBC)", "test_profile": "Malarial Parasite (QBC)", "test_price": 400, "department": "HAEMATOLOGY", "hmsCode": "000427", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "< 1    Parasite   : + 1-10   Parasites  : ++ 11-100 Parasites  : +++ > 100  Parasites  : +++", "resultUnit": "per QBC Field", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "QBC", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.643757", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 122, "min_sample_qty": null, "price": 400, "reference_range": "< 1    Parasite   : + 1-10   Parasites  : ++ 11-100 Parasites  : +++ > 100  Parasites  : +++", "reporting_days": 0, "result_type": "Pick List", "result_unit": "per QBC Field", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000427", "test_done_on": "all", "test_name": "Malarial Parasite (QBC)", "updated_at": "2025-07-09T12:20:55.643760"}}], "bill_amount": 700, "other_charges": 0, "discount_percent": 0, "subtotal": 700, "discount": 0, "gst_rate": 18, "gst_amount": 126, "tax": 126, "total_amount": 400, "paid_amount": 800, "balance": -328.0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "notes": "", "branch": 1, "created_at": "2025-08-08T15:57:00.962374", "updated_at": "2025-08-09T17:35:52.271473", "tenant_id": 1, "created_by": 4, "test_items": [{"id": 1, "name": "Malarial Parasite (QBC)", "amount": 400, "status": "Pending"}], "tax_amount": 72.0, "net_amount": 472.0}, {"id": 76, "invoice_number": "INV00076", "sid_number": "411", "patient_id": 72, "items": [{"id": 1754649489050, "test_id": 11, "test_master_id": 11, "testName": "BLOOD GROUP&RH-GEL METHOD", "test_name": "BLOOD GROUP&RH-GEL METHOD", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000428", "display_name": "BLOOD GROUP&RH-GEL METHOD", "short_name": "", "international_code": "", "method": "GEL METHOD", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "EDTA Container", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 11, "testName": "BLOOD GROUP&RH-GEL METHOD", "test_profile": "BLOOD GROUP&RH-GEL METHOD", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000428", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "GEL METHOD", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.639371", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 75, "min_sample_qty": null, "price": 20, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "000428", "test_done_on": "all", "test_name": "BLOOD GROUP&RH-GEL METHOD", "updated_at": "2025-07-09T12:20:55.639374"}}, {"amount": 100, "applicable_to": "Both", "department": "HAEMATOLOGY", "description": "NASAL SMEAR FOR EOSINOPHILS", "display_name": "NASAL SMEAR FOR EOSINOPHILS", "hms_code": "000425", "id": 1754736027501, "method": "", "price": 100, "primary_specimen": "NASAL SMEAR", "quantity": 1, "reference_range": "", "reporting_days": 0, "short_name": "", "specimen": "NASAL SMEAR", "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_done_on": "all", "test_id": 49, "test_master_id": 49, "test_name": "NASAL SMEAR FOR EOSINOPHILS", "test_price": 100, "total": 100, "unit_price": 100, "status": "Pending", "test_master_data": {"id": 49, "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_profile": "NASAL SMEAR FOR EOSINOPHILS", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000425", "specimen": "NASAL SMEAR", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644783", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": 28, "test_code": "000425", "test_done_on": "all", "test_name": "NASAL SMEAR FOR EOSINOPHILS", "updated_at": "2025-07-09T12:20:55.644786"}}, {"amount": 1000, "description": "Immature Platelet Fraction (IPF)", "display_name": "Immature Platelet Fraction (IPF)", "id": 1754915168567, "price": 1000, "quantity": 1, "test_id": 32, "test_name": "Immature Platelet Fraction (IPF)", "unit_price": 1000, "total": 1000, "status": "Pending", "test_master_data": {"id": 32, "testName": "Immature Platelet Fraction (IPF)", "testProfile": "Immature Platelet Fraction (IPF)", "testPrice": 1000, "department": "HAEMATOLOGY", "hmsCode": "001537", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": 5, "cutoffTime": "", "referenceRange": null, "resultUnit": null, "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "Fluorescent Flow Cytometry", "instructions": null, "notes": null, "minSampleQty": null, "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "containerCode": 7, "createdAt": "2025-07-09T12:20:55.642586", "excelSource": true, "methodCode": 157, "price": 1000, "resultType": "Pick List", "shortName": "IPF", "sourceSheet": "HAEMATOLOGY", "specimenCode": 17, "testCode": "001537", "updatedAt": "2025-07-09T12:20:55.642589"}}], "bill_amount": 20, "other_charges": 0, "discount_percent": 0, "subtotal": 20, "discount": 0, "gst_rate": 18, "gst_amount": 3.6, "tax": 3.6, "total_amount": 1100, "paid_amount": 20, "balance": 1278.0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "notes": "", "branch": 1, "created_at": "2025-08-08T16:08:22.709395", "updated_at": "2025-08-11T17:56:08.565975", "tenant_id": 1, "created_by": 4, "test_items": [{"id": 1, "name": "NASAL SMEAR FOR EOSINOPHILS", "amount": 100, "status": "Pending"}, {"id": 2, "amount": 1000, "applicable_to": "Both", "container": null, "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 1, "department": "HAEMATOLOGY", "display_name": "Immature Platelet Fraction (IPF)", "hms_code": "001537", "instructions": "", "international_code": "", "interpretation": "", "method": "Fluorescent Flow Cytometry", "min_sample_qty": "", "price": 1000, "primary_specimen": "", "quantity": 1, "reference_range": "", "reporting_days": 5, "result_unit": "", "sample_received": false, "sample_received_timestamp": null, "sample_status": "Not Received", "service_time": "", "short_name": "IPF", "specimen": "EDTA BLOOD", "suffix_desc": "", "test_done_on": "all", "test_master_data": {"id": 32, "testName": "Immature Platelet Fraction (IPF)", "testProfile": "Immature Platelet Fraction (IPF)", "testPrice": 1000, "department": "HAEMATOLOGY", "hmsCode": "001537", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": 5, "cutoffTime": "", "referenceRange": null, "resultUnit": null, "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "Fluorescent Flow Cytometry", "instructions": null, "notes": null, "minSampleQty": null, "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "containerCode": 7, "createdAt": "2025-07-09T12:20:55.642586", "excelSource": true, "methodCode": 157, "price": 1000, "resultType": "Pick List", "shortName": "IPF", "sourceSheet": "HAEMATOLOGY", "specimenCode": 17, "testCode": "001537", "updatedAt": "2025-07-09T12:20:55.642589"}, "test_master_id": 0, "test_name": "Immature Platelet Fraction (IPF)", "test_price": null, "test_suffix": "", "unacceptable_conditions": []}], "tax_amount": 198.0, "net_amount": 1298.0}, {"id": 77, "invoice_number": "INV00077", "sid_number": "414", "patient_id": 73, "items": [{"id": 1754649912181, "test_id": 46, "test_master_id": 46, "testName": "MICROFILARIA (MF)  by QBC", "test_name": "MICROFILARIA (MF)  by QBC", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000431", "display_name": "MICROFILARIA (MF)  by QBC", "short_name": "QBC", "international_code": "", "method": "QBC", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "", "reference_range": "( QBC )", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 46, "testName": "MICROFILARIA (MF)  by QBC", "test_profile": "MICROFILARIA (MF)  by QBC", "test_price": 400, "department": "HAEMATOLOGY", "hmsCode": "000431", "specimen": "EDTA BLOOD", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "( QBC )", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "QBC", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644445", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 122, "min_sample_qty": null, "price": 400, "reference_range": "( QBC )", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "QBC", "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "000431", "test_done_on": "all", "test_name": "MICROFILARIA (MF)  by QBC", "updated_at": "2025-07-09T12:20:55.644448"}}, {"amount": 1000, "applicable_to": "Both", "department": "HAEMATOLOGY", "description": "FDP", "display_name": "FDP", "hms_code": "000076", "id": 1754735546875, "method": "Agglutination", "price": 1000, "primary_specimen": "Citrate Plasma", "quantity": 1, "reference_range": "Negative", "reporting_days": 0, "short_name": "FDP", "specimen": "Citrate Plasma", "testName": "FDP", "test_done_on": "all", "test_id": 23, "test_master_id": 23, "test_name": "FDP", "test_price": 1000, "total": 1000, "unit_price": 1000, "status": "Pending", "test_master_data": {"id": 23, "testName": "FDP", "test_profile": "FDP", "test_price": 1000, "department": "HAEMATOLOGY", "hmsCode": "000076", "specimen": "Citrate Plasma", "container": "Citrate Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Negative", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "Agglutination", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 8, "created_at": "2025-07-09T12:20:55.640653", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 7, "min_sample_qty": null, "price": 1000, "reference_range": "Negative", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "FDP", "source_sheet": "HAEMATOLOGY", "specimen_code": 14, "test_code": "000076", "test_done_on": "all", "test_name": "FDP", "updated_at": "2025-07-09T12:20:55.640656"}}, {"amount": 20, "description": "CLOTTING TIME", "display_name": "CLOTTING TIME", "id": 1754739347348, "price": 20, "quantity": 1, "test_id": 13, "test_name": "CLOTTING TIME", "unit_price": 20, "total": 20, "status": "Pending", "test_master_data": {"id": 13, "testName": "CLOTTING TIME", "test_profile": "CLOTTING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000347", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "5 - 10", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "<PERSON> and <PERSON> method", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639584", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 106, "min_sample_qty": null, "price": 20, "reference_range": "5 - 10", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000347", "test_done_on": "all", "test_name": "CLOTTING TIME", "updated_at": "2025-07-09T12:20:55.639586"}}, {"amount": 20, "description": "BLEEDING TIME", "display_name": "BLEEDING TIME", "id": 1754740740394, "price": 20, "quantity": 1, "test_id": 10, "test_name": "BLEEDING TIME", "unit_price": 20, "total": 20, "status": "Pending", "test_master_data": {"id": 10, "testName": "BLEEDING TIME", "test_profile": "BLEEDING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000342", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "1 to 6", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IVY", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639261", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 100, "min_sample_qty": null, "price": 20, "reference_range": "1 to 6", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": "BT", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000342", "test_done_on": "all", "test_name": "BLEEDING TIME", "updated_at": "2025-07-09T12:20:55.639264"}}], "bill_amount": 400, "other_charges": 0, "discount_percent": 0, "subtotal": 400, "discount": 0, "gst_rate": 18, "gst_amount": 72, "tax": 72, "total_amount": 1060, "paid_amount": 400, "balance": 850.8, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "notes": "", "branch": 1, "created_at": "2025-08-08T16:15:28.319915", "updated_at": "2025-08-09T17:29:00.394364", "tenant_id": 1, "created_by": 4, "test_items": [{"id": 1, "name": "BLOOD GROUP&RH-GEL METHOD", "amount": 20, "status": "Pending"}, {"id": 2, "name": "FDP", "amount": 1000, "status": "Pending"}, {"id": 3, "name": "CLOTTING TIME", "amount": 20, "status": "Pending"}, {"id": 4, "name": "BLEEDING TIME", "amount": 20, "status": "Pending"}], "tax_amount": 190.79999999999998, "net_amount": 1250.8}, {"id": 78, "invoice_number": "INV00078", "sid_number": "427", "patient_id": 74, "items": [{"amount": 80, "applicable_to": "Both", "container": "EDTA Container", "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 0, "department": "HAEMATOLOGY", "display_name": "ESR", "hms_code": "000353", "id": 1754660118937, "instructions": "", "international_code": "", "interpretation": "", "method": "<PERSON><PERSON><PERSON><PERSON> Method", "min_sample_qty": "", "price": 80, "primary_specimen": "EDTA BLOOD", "quantity": 1, "reference_range": "", "reporting_days": 0, "result_unit": "", "service_time": "", "short_name": "ESR", "specimen": "EDTA BLOOD", "suffix_desc": "", "testName": "ESR", "test_done_on": "all", "test_id": 21, "test_master_data": {"applicableTo": "Both", "applicable_to": "Both", "container": "EDTA Container", "container_code": 7, "created_at": "2025-07-09T12:20:55.640439", "criticalHigh": null, "criticalLow": null, "critical_high": null, "critical_low": null, "cutoffTime": "", "decimals": 0, "department": "HAEMATOLOGY", "excel_source": true, "hmsCode": "000353", "id": 21, "instructions": null, "isActive": true, "is_active": true, "method": "<PERSON><PERSON><PERSON><PERSON> Method", "method_code": 143, "minSampleQty": "", "min_sample_qty": null, "notes": null, "price": 80, "referenceRange": "", "reference_range": null, "reportingDays": "", "reporting_days": 0, "resultUnit": "", "result_type": "-", "result_unit": null, "serviceTime": "", "short_name": "ESR", "source_sheet": "HAEMATOLOGY", "specimen": "EDTA BLOOD", "specimen_code": 17, "testDoneOn": "all", "testName": "ESR", "test_code": "000353", "test_done_on": "all", "test_name": "ESR", "test_price": 80, "test_profile": "ESR", "updated_at": "2025-07-09T12:20:55.640442"}, "test_master_id": 21, "test_name": "ESR", "test_price": 80, "test_suffix": "", "unacceptable_conditions": ""}, {"amount": 100, "applicable_to": "Both", "department": "HAEMATOLOGY", "description": "NASAL SMEAR FOR EOSINOPHILS", "display_name": "NASAL SMEAR FOR EOSINOPHILS", "hms_code": "000425", "id": 1754736227267, "method": "", "price": 100, "primary_specimen": "NASAL SMEAR", "quantity": 1, "reference_range": "", "reporting_days": 0, "short_name": "", "specimen": "NASAL SMEAR", "status": "Pending", "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_done_on": "all", "test_id": 49, "test_master_data": {"applicableTo": "Both", "applicable_to": "Both", "container": null, "container_code": null, "created_at": "2025-07-09T12:20:55.644783", "criticalHigh": null, "criticalLow": null, "critical_high": null, "critical_low": null, "cutoffTime": "", "decimals": 0, "department": "HAEMATOLOGY", "excel_source": true, "hmsCode": "000425", "id": 49, "instructions": null, "isActive": true, "is_active": true, "method": null, "method_code": null, "minSampleQty": "", "min_sample_qty": null, "notes": null, "price": 100, "referenceRange": "", "reference_range": null, "reportingDays": "", "reporting_days": 0, "resultUnit": "", "result_type": "Pick List", "result_unit": null, "serviceTime": "", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen": "NASAL SMEAR", "specimen_code": 28, "testDoneOn": "all", "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_code": "000425", "test_done_on": "all", "test_name": "NASAL SMEAR FOR EOSINOPHILS", "test_price": 100, "test_profile": "NASAL SMEAR FOR EOSINOPHILS", "updated_at": "2025-07-09T12:20:55.644786"}, "test_master_id": 49, "test_name": "NASAL SMEAR FOR EOSINOPHILS", "test_price": 100, "total": 100, "unit_price": 100}, {"amount": 100, "applicable_to": "Both", "department": "HAEMATOLOGY", "description": "NASAL SMEAR FOR EOSINOPHILS", "display_name": "NASAL SMEAR FOR EOSINOPHILS", "hms_code": "000425", "id": 1754737085810, "method": "", "price": 100, "primary_specimen": "NASAL SMEAR", "quantity": 1, "reference_range": "", "reporting_days": 0, "short_name": "", "specimen": "NASAL SMEAR", "status": "Pending", "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_done_on": "all", "test_id": 49, "test_master_data": {"applicableTo": "Both", "applicable_to": "Both", "container": null, "container_code": null, "created_at": "2025-07-09T12:20:55.644783", "criticalHigh": null, "criticalLow": null, "critical_high": null, "critical_low": null, "cutoffTime": "", "decimals": 0, "department": "HAEMATOLOGY", "excel_source": true, "hmsCode": "000425", "id": 49, "instructions": null, "isActive": true, "is_active": true, "method": null, "method_code": null, "minSampleQty": "", "min_sample_qty": null, "notes": null, "price": 100, "referenceRange": "", "reference_range": null, "reportingDays": "", "reporting_days": 0, "resultUnit": "", "result_type": "Pick List", "result_unit": null, "serviceTime": "", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen": "NASAL SMEAR", "specimen_code": 28, "testDoneOn": "all", "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_code": "000425", "test_done_on": "all", "test_name": "NASAL SMEAR FOR EOSINOPHILS", "test_price": 100, "test_profile": "NASAL SMEAR FOR EOSINOPHILS", "updated_at": "2025-07-09T12:20:55.644786"}, "test_master_id": 49, "test_name": "NASAL SMEAR FOR EOSINOPHILS", "test_price": 100, "total": 100, "unit_price": 100}, {"amount": 1200, "description": "MYOGLOBIN-SERUM", "display_name": "MYOGLOBIN-SERUM", "id": 1754738171407, "price": 1200, "quantity": 1, "status": "Pending", "test_id": 48, "test_master_data": {"applicableTo": "Both", "applicable_to": "Both", "container": null, "container_code": null, "created_at": "2025-07-09T12:20:55.644672", "criticalHigh": null, "criticalLow": null, "critical_high": null, "critical_low": null, "cutoffTime": "", "decimals": 2, "department": "HAEMATOLOGY", "excel_source": true, "hmsCode": "000426", "id": 48, "instructions": null, "isActive": true, "is_active": true, "method": "ECLIA", "method_code": 52, "minSampleQty": "", "min_sample_qty": null, "notes": null, "price": 1200, "referenceRange": "25-58", "reference_range": "25-58", "reportingDays": "", "reporting_days": 0, "resultUnit": "ug/L", "result_type": "Numeric", "result_unit": "ug/L", "serviceTime": "", "short_name": "MYOS", "source_sheet": "HAEMATOLOGY", "specimen": "Serum", "specimen_code": 64, "testDoneOn": "all", "testName": "MYOGLOBIN-SERUM", "test_code": "000426", "test_done_on": "all", "test_name": "MYOGLOBIN-SERUM", "test_price": 1200, "test_profile": "MYOGLOBIN-SERUM", "updated_at": "2025-07-09T12:20:55.644675"}, "test_name": "MYOGLOBIN-SERUM", "total": 1200, "unit_price": 1200}, {"amount": 100, "description": "MCV", "display_name": "MCV", "id": 1754740796320, "price": 100, "quantity": 1, "status": "Pending", "test_id": 43, "test_master_data": {"applicableTo": "Both", "applicable_to": "Both", "container": "EDTA Container", "container_code": 7, "created_at": "2025-07-09T12:20:55.644109", "criticalHigh": null, "criticalLow": null, "critical_high": null, "critical_low": null, "cutoffTime": "", "decimals": 1, "department": "HAEMATOLOGY", "excel_source": true, "hmsCode": "000371", "id": 43, "instructions": null, "isActive": true, "is_active": true, "method": "Calculated", "method_code": 20, "minSampleQty": "", "min_sample_qty": null, "notes": null, "price": 100, "referenceRange": "", "reference_range": null, "reportingDays": "", "reporting_days": 0, "resultUnit": "fl", "result_type": "Numeric", "result_unit": "fl", "serviceTime": "", "short_name": "MCV", "source_sheet": "HAEMATOLOGY", "specimen": "EDTA BLOOD", "specimen_code": 17, "testDoneOn": "all", "testName": "MCV", "test_code": "000371", "test_done_on": "all", "test_name": "MCV", "test_price": 100, "test_profile": "MCV", "updated_at": "2025-07-09T12:20:55.644112"}, "test_name": "MCV", "total": 100, "unit_price": 100}], "bill_amount": 80, "other_charges": 0, "discount_percent": 0, "subtotal": 80, "discount": 0, "gst_rate": 18, "gst_amount": 14.4, "tax": 14.4, "total_amount": 147.0, "paid_amount": 80, "balance": 1690, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "notes": "", "branch": 1, "created_at": "2025-08-08T19:05:57.747571", "updated_at": "2025-08-09T17:29:56.320575", "tenant_id": 1, "created_by": 4, "test_items": [{"amount": 100, "id": 1, "name": "NASAL SMEAR FOR EOSINOPHILS", "status": "Pending"}, {"amount": 100, "id": 2, "name": "NASAL SMEAR FOR EOSINOPHILS", "status": "Pending"}, {"amount": 1200, "id": 3, "name": "MYOGLOBIN-SERUM", "status": "Pending"}, {"amount": 100, "id": 4, "name": "MCV", "status": "Pending"}], "tax_amount": 270, "net_amount": 1770, "patient": {"first_name": "rashith mohamed", "id": 74, "last_name": "#74"}}, {"id": 79, "invoice_number": "INV00079", "sid_number": "438", "patient_id": 10, "items": [{"id": 1754661835372, "test_id": 49, "test_master_id": 49, "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_name": "NASAL SMEAR FOR EOSINOPHILS", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000425", "display_name": "NASAL SMEAR FOR EOSINOPHILS", "short_name": "", "international_code": "", "method": "", "primary_specimen": "NASAL SMEAR", "specimen": "NASAL SMEAR", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 49, "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_profile": "NASAL SMEAR FOR EOSINOPHILS", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000425", "specimen": "NASAL SMEAR", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644783", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": 28, "test_code": "000425", "test_done_on": "all", "test_name": "NASAL SMEAR FOR EOSINOPHILS", "updated_at": "2025-07-09T12:20:55.644786"}}, {"id": 1754661844278, "test_id": 48, "test_master_id": 48, "testName": "MYOGLOBIN-SERUM", "test_name": "MYOGLOBIN-SERUM", "amount": 1200, "price": 1200, "test_price": 1200, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000426", "display_name": "MYOGLOBIN-SERUM", "short_name": "MYOS", "international_code": "", "method": "ECLIA", "primary_specimen": "Serum", "specimen": "Serum", "container": "", "reference_range": "25-58", "result_unit": "ug/L", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 48, "testName": "MYOGLOBIN-SERUM", "test_profile": "MYOGLOBIN-SERUM", "test_price": 1200, "department": "HAEMATOLOGY", "hmsCode": "000426", "specimen": "Serum", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "25-58", "resultUnit": "ug/L", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "ECLIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644672", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 52, "min_sample_qty": null, "price": 1200, "reference_range": "25-58", "reporting_days": 0, "result_type": "Numeric", "result_unit": "ug/L", "short_name": "MYOS", "source_sheet": "HAEMATOLOGY", "specimen_code": 64, "test_code": "000426", "test_done_on": "all", "test_name": "MYOGLOBIN-SERUM", "updated_at": "2025-07-09T12:20:55.644675"}}, {"id": 1754661864630, "test_id": 27, "test_master_id": 27, "testName": "GIEMSA STAINING", "test_name": "GIEMSA STAINING", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000423", "display_name": "GIEMSA STAINING", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 27, "testName": "GIEMSA STAINING", "test_profile": "GIEMSA STAINING", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000423", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.641201", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "No Unit / Ref. Value", "result_unit": null, "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000423", "test_done_on": "all", "test_name": "GIEMSA STAINING", "updated_at": "2025-07-09T12:20:55.641206"}}, {"id": 1754661885502, "test_id": 85, "test_master_id": 85, "testName": "BENCE JONES PROTEIN-URINE", "test_name": "BENCE JONES PROTEIN-URINE", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "CLINICAL_PATHOLOGY", "hms_code": "000316", "display_name": "BENCE JONES PROTEIN-URINE", "short_name": "BJP", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "NEGATIVE", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 85, "testName": "BENCE JONES PROTEIN-URINE", "test_profile": "BENCE JONES PROTEIN-URINE", "test_price": 100, "department": "CLINICAL_PATHOLOGY", "hmsCode": "000316", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NEGATIVE", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.740444", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 100, "reference_range": "NEGATIVE", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "BJP", "source_sheet": "Clinical Pathology", "specimen_code": null, "test_code": "000316", "test_done_on": "all", "test_name": "BENCE JONES PROTEIN-URINE", "updated_at": "2025-07-09T12:20:55.740447"}}, {"amount": 0, "applicable_to": "Both", "department": "HAEMATOLOGY", "description": "17 HYDROXY CORTICO STEROID 24 HR URINE", "display_name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "hms_code": "000003", "id": 1754735619367, "method": "Column Chromatography", "price": 0, "primary_specimen": "24 H<PERSON> <PERSON><PERSON>", "quantity": 1, "reference_range": "< 1 Years     :  <=  1.0 (Both) 1 - 10 Years  :  3 - 6 (Both) > 10 Years    :  3 - 10 (Male)", "reporting_days": 10, "short_name": "17HY", "specimen": "24 H<PERSON> <PERSON><PERSON>", "testName": "17 HYDROXY CORTICO STEROID 24 HR URINE", "test_done_on": "all", "test_id": 16, "test_master_id": 16, "test_name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "test_price": 0, "total": 0, "unit_price": 0, "status": "Pending", "test_master_data": {"id": 16, "testName": "17 HYDROXY CORTICO STEROID 24 HR URINE", "test_profile": "17 HYDROXY CORTICO STEROID 24 HR URINE", "test_price": 0, "department": "HAEMATOLOGY", "hmsCode": "000003", "specimen": "24 H<PERSON> <PERSON><PERSON>", "container": "<PERSON><PERSON><PERSON>er", "serviceTime": "", "reportingDays": 10, "cutoffTime": "", "referenceRange": "< 1 Years     :  <=  1.0 (Both) 1 - 10 Years  :  3 - 6 (Both) > 10 Years    :  3 - 10 (Male)", "resultUnit": "mg/24hrs", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "Column Chromatography", "instructions": "10 ml of 50% HCL as a preservative, Total volume to be mentioned", "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 13, "created_at": "2025-07-09T12:20:55.639903", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 45, "min_sample_qty": null, "price": 0, "reference_range": "< 1 Years     :  <=  1.0 (Both) 1 - 10 Years  :  3 - 6 (Both) > 10 Years    :  3 - 10 (Male)", "reporting_days": 10, "result_type": null, "result_unit": "mg/24hrs", "short_name": "17HY", "source_sheet": "HAEMATOLOGY", "specimen_code": 3, "test_code": "000003", "test_done_on": "all", "test_name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "updated_at": "2025-07-09T12:20:55.639906"}}], "bill_amount": 1500, "other_charges": 0, "discount_percent": 0, "subtotal": 1500, "discount": 0, "gst_rate": 18, "gst_amount": 270, "tax": 270, "total_amount": 800, "paid_amount": 400, "balance": 400, "payment_method": "", "payment_status": "Partial", "status": "Pending", "invoice_date": "2025-08-08", "due_date": "2025-09-07", "notes": "", "branch": 1, "created_at": "2025-08-08T19:35:08.166291", "updated_at": "2025-08-09T16:03:39.367452", "tenant_id": 1, "created_by": 4, "test_items": [{"id": 1, "name": "FIBRINOGEN", "amount": 700, "status": "Pending"}, {"id": 2, "name": "MCHC", "amount": 100, "status": "Pending"}, {"id": 3, "name": "17 HYDROXY CORTICO STEROID 24 HR URINE", "amount": 0, "status": "Pending"}]}, {"id": 80, "invoice_number": "INV00080", "sid_number": "440", "patient_id": 51, "items": [{"id": 1754742966866, "test_id": 28, "test_master_id": 28, "testName": "Haemoglobin", "test_name": "Haemoglobin", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000361", "display_name": "Haemoglobin", "short_name": "HB", "international_code": "", "method": "Non-Cyanide Haemoglobin Analysis", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "EDTA Container", "reference_range": "12.0 - 16.0\n14.0 - 18.0", "result_unit": "g/dL", "decimals": 1, "critical_low": 7, "critical_high": 19, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 28, "testName": "Haemoglobin", "test_profile": "Haemoglobin", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000361", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "12.0 - 16.0\n14.0 - 18.0", "resultUnit": "g/dL", "decimals": 1, "criticalLow": 7, "criticalHigh": 19, "method": "Non-Cyanide Haemoglobin Analysis", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.641620", "critical_high": 19, "critical_low": 7, "excel_source": true, "is_active": true, "method_code": 12, "min_sample_qty": null, "price": 100, "reference_range": "12.0 - 16.0\n14.0 - 18.0", "reporting_days": 0, "result_type": "-", "result_unit": "g/dL", "short_name": "HB", "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "000361", "test_done_on": "all", "test_name": "Haemoglobin", "updated_at": "2025-07-09T12:20:55.641625"}}, {"id": 1754742972642, "test_id": 39, "test_master_id": 39, "testName": "MALARIAL ANTIGEN (Vivax & Falciparum)", "test_name": "MALARIAL ANTIGEN (Vivax & Falciparum)", "amount": 600, "price": 600, "test_price": 600, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000430", "display_name": "MALARIAL ANTIGEN (Vivax & Falciparum)", "short_name": "", "international_code": "", "method": "IC", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "", "reference_range": "NEGATIVE", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 39, "testName": "MALARIAL ANTIGEN (Vivax & Falciparum)", "test_profile": "MALARIAL ANTIGEN (Vivax & Falciparum)", "test_price": 600, "department": "HAEMATOLOGY", "hmsCode": "000430", "specimen": "EDTA BLOOD", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NEGATIVE", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IC", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.643640", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 80, "min_sample_qty": null, "price": 600, "reference_range": "NEGATIVE", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "000430", "test_done_on": "all", "test_name": "MALARIAL ANTIGEN (Vivax & Falciparum)", "updated_at": "2025-07-09T12:20:55.643642"}}, {"amount": 1200, "description": "MYOGLOBIN-SERUM", "display_name": "MYOGLOBIN-SERUM", "id": 1754743209420, "price": 1200, "quantity": 1, "test_id": 48, "test_name": "MYOGLOBIN-SERUM", "unit_price": 1200, "total": 1200, "status": "Pending", "test_master_data": {"id": 48, "testName": "MYOGLOBIN-SERUM", "test_profile": "MYOGLOBIN-SERUM", "test_price": 1200, "department": "HAEMATOLOGY", "hmsCode": "000426", "specimen": "Serum", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "25-58", "resultUnit": "ug/L", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "ECLIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644672", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 52, "min_sample_qty": null, "price": 1200, "reference_range": "25-58", "reporting_days": 0, "result_type": "Numeric", "result_unit": "ug/L", "short_name": "MYOS", "source_sheet": "HAEMATOLOGY", "specimen_code": 64, "test_code": "000426", "test_done_on": "all", "test_name": "MYOGLOBIN-SERUM", "updated_at": "2025-07-09T12:20:55.644675"}}, {"amount": 300, "description": "Haemoglobin, Urine", "display_name": "Haemoglobin, Urine", "id": 1754744923758, "price": 300, "quantity": 1, "test_id": 29, "test_name": "Haemoglobin, Urine", "unit_price": 300, "total": 300, "status": "Pending", "test_master_data": {"id": 29, "testName": "Haemoglobin, Urine", "test_profile": "Haemoglobin, Urine", "test_price": 300, "department": "HAEMATOLOGY", "hmsCode": "000280", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.641973", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": "Biochemical", "min_sample_qty": null, "price": 300, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": 15, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000280", "test_done_on": "all", "test_name": "Haemoglobin, Urine", "updated_at": "2025-07-09T12:20:55.641991"}}], "bill_amount": 700, "other_charges": 0, "discount_percent": 0, "subtotal": 700, "discount": 0, "gst_rate": 18, "gst_amount": 126, "tax": 126, "total_amount": 1500, "paid_amount": 700, "balance": 1070.0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-09", "due_date": "2025-09-08", "notes": "", "branch": 1, "created_at": "2025-08-09T18:06:27.143918", "updated_at": "2025-08-09T18:38:43.756050", "tenant_id": 1, "created_by": 4, "test_items": [{"id": 1, "name": "MYOGLOBIN-SERUM", "amount": 1200, "status": "Pending"}, {"id": 2, "name": "Haemoglobin, Urine", "amount": 300, "status": "Pending"}], "tax_amount": 270.0, "net_amount": 1770.0}, {"id": 81, "invoice_number": "INV00081", "sid_number": "442", "patient_id": 75, "items": [{"amount": 1200, "applicable_to": "Both", "container": "", "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 2, "department": "HAEMATOLOGY", "display_name": "MYOGLOBIN-SERUM", "hms_code": "000426", "id": 1754908582885, "instructions": "", "international_code": "", "interpretation": "", "method": "ECLIA", "min_sample_qty": "", "price": 1200, "primary_specimen": "Serum", "quantity": 1, "reference_range": "25-58", "reporting_days": 0, "result_unit": "ug/L", "service_time": "", "short_name": "MYOS", "specimen": "Serum", "suffix_desc": "", "testName": "MYOGLOBIN-SERUM", "test_done_on": "all", "test_id": 48, "test_master_data": {"applicableTo": "Both", "applicable_to": "Both", "container": null, "container_code": null, "created_at": "2025-07-09T12:20:55.644672", "criticalHigh": null, "criticalLow": null, "critical_high": null, "critical_low": null, "cutoffTime": "", "decimals": 2, "department": "HAEMATOLOGY", "excel_source": true, "hmsCode": "000426", "id": 48, "instructions": null, "isActive": true, "is_active": true, "method": "ECLIA", "method_code": 52, "minSampleQty": "", "min_sample_qty": null, "notes": null, "price": 1200, "referenceRange": "25-58", "reference_range": "25-58", "reportingDays": "", "reporting_days": 0, "resultUnit": "ug/L", "result_type": "Numeric", "result_unit": "ug/L", "serviceTime": "", "short_name": "MYOS", "source_sheet": "HAEMATOLOGY", "specimen": "Serum", "specimen_code": 64, "testDoneOn": "all", "testName": "MYOGLOBIN-SERUM", "test_code": "000426", "test_done_on": "all", "test_name": "MYOGLOBIN-SERUM", "test_price": 1200, "test_profile": "MYOGLOBIN-SERUM", "updated_at": "2025-07-09T12:20:55.644675"}, "test_master_id": 48, "test_name": "MYOGLOBIN-SERUM", "test_price": 1200, "test_suffix": "", "unacceptable_conditions": ""}, {"amount": 1200, "description": "MYOGLOBIN-SERUM", "display_name": "MYOGLOBIN-SERUM", "id": 1754913755615, "price": 1200, "quantity": 1, "status": "Pending", "test_id": 48, "test_master_data": {"applicableTo": "Both", "container": null, "containerCode": null, "createdAt": "2025-07-09T12:20:55.644672", "criticalHigh": null, "criticalLow": null, "cutoffTime": "", "decimals": 2, "department": "HAEMATOLOGY", "excelSource": true, "hmsCode": "000426", "id": 48, "instructions": null, "isActive": true, "method": "ECLIA", "methodCode": 52, "minSampleQty": null, "notes": null, "price": 1200, "referenceRange": "25-58", "reportingDays": 0, "resultType": "Numeric", "resultUnit": "ug/L", "serviceTime": "", "shortName": "MYOS", "sourceSheet": "HAEMATOLOGY", "specimen": "Serum", "specimenCode": 64, "testCode": "000426", "testDoneOn": "all", "testName": "MYOGLOBIN-SERUM", "testPrice": 1200, "testProfile": "MYOGLOBIN-SERUM", "updatedAt": "2025-07-09T12:20:55.644675"}, "test_name": "MYOGLOBIN-SERUM", "total": 1200, "unit_price": 1200}, {"amount": 20, "description": "CLOTTING TIME", "display_name": "CLOTTING TIME", "id": 1754913804014, "price": 20, "quantity": 1, "status": "Pending", "test_id": 13, "test_master_data": {"applicableTo": "Both", "container": null, "containerCode": null, "createdAt": "2025-07-09T12:20:55.639584", "criticalHigh": null, "criticalLow": null, "cutoffTime": "", "decimals": 0, "department": "HAEMATOLOGY", "excelSource": true, "hmsCode": "000347", "id": 13, "instructions": null, "isActive": true, "method": "<PERSON> and <PERSON> method", "methodCode": 106, "minSampleQty": null, "notes": null, "price": 20, "referenceRange": "5 - 10", "reportingDays": 0, "resultType": "Pick List", "resultUnit": "minutes", "serviceTime": "", "shortName": null, "sourceSheet": "HAEMATOLOGY", "specimen": null, "specimenCode": null, "testCode": "000347", "testDoneOn": "all", "testName": "CLOTTING TIME", "testPrice": 20, "testProfile": "CLOTTING TIME", "updatedAt": "2025-07-09T12:20:55.639586"}, "test_name": "CLOTTING TIME", "total": 20, "unit_price": 20}, {"amount": 400, "description": "Malarial Parasite (QBC)", "display_name": "Malarial Parasite (QBC)", "id": 1754914112070, "price": 400, "quantity": 1, "status": "Pending", "test_id": 40, "test_master_data": {"applicableTo": "Both", "container": null, "containerCode": null, "createdAt": "2025-07-09T12:20:55.643757", "criticalHigh": null, "criticalLow": null, "cutoffTime": "", "decimals": 0, "department": "HAEMATOLOGY", "excelSource": true, "hmsCode": "000427", "id": 40, "instructions": null, "isActive": true, "method": "QBC", "methodCode": 122, "minSampleQty": null, "notes": null, "price": 400, "referenceRange": "< 1    Parasite   : + 1-10   Parasites  : ++ 11-100 Parasites  : +++ > 100  Parasites  : +++", "reportingDays": 0, "resultType": "Pick List", "resultUnit": "per QBC Field", "serviceTime": "", "shortName": null, "sourceSheet": "HAEMATOLOGY", "specimen": null, "specimenCode": null, "testCode": "000427", "testDoneOn": "all", "testName": "Malarial Parasite (QBC)", "testPrice": 400, "testProfile": "Malarial Parasite (QBC)", "updatedAt": "2025-07-09T12:20:55.643760"}, "test_name": "Malarial Parasite (QBC)", "total": 400, "unit_price": 400}, {"amount": 100, "description": "Haemoglobin", "display_name": "Haemoglobin", "id": 1754914779585, "price": 100, "quantity": 1, "status": "Pending", "test_id": 28, "test_master_data": {"applicableTo": "Both", "container": "EDTA Container", "containerCode": 7, "createdAt": "2025-07-09T12:20:55.641620", "criticalHigh": 19, "criticalLow": 7, "cutoffTime": "", "decimals": 1, "department": "HAEMATOLOGY", "excelSource": true, "hmsCode": "000361", "id": 28, "instructions": null, "isActive": true, "method": "Non-Cyanide Haemoglobin Analysis", "methodCode": 12, "minSampleQty": null, "notes": null, "price": 100, "referenceRange": "12.0 - 16.0\n14.0 - 18.0", "reportingDays": 0, "resultType": "-", "resultUnit": "g/dL", "serviceTime": "", "shortName": "HB", "sourceSheet": "HAEMATOLOGY", "specimen": "EDTA BLOOD", "specimenCode": 17, "testCode": "000361", "testDoneOn": "all", "testName": "Haemoglobin", "testPrice": 100, "testProfile": "Haemoglobin", "updatedAt": "2025-07-09T12:20:55.641625"}, "test_name": "Haemoglobin", "total": 100, "unit_price": 100}], "bill_amount": 1200, "other_charges": 0, "discount_percent": 0, "subtotal": 1200, "discount": 0, "gst_rate": 18, "gst_amount": 216, "tax": 216, "total_amount": 801.0, "paid_amount": 1200, "balance": 8287.2, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-11", "due_date": "2025-09-10", "notes": "", "branch": 1, "created_at": "2025-08-11T16:06:36.844090", "updated_at": "2025-08-11T17:49:39.577643", "tenant_id": 1, "created_by": 4, "test_items": [{"amount": 100, "id": 1, "name": "NASAL SMEAR FOR EOSINOPHILS", "status": "Pending"}, {"amount": 100, "id": 2, "name": "ABSOLUTE NEUTROPHIL COUNT", "status": "Pending"}, {"amount": 6100, "id": 3, "name": "Chromosome Analysis - Product of Conception", "status": "Pending"}, {"amount": 20, "id": 4, "name": "CLOTTING TIME", "status": "Pending"}, {"amount": 1200, "applicable_to": "Both", "container": null, "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 1, "department": "HAEMATOLOGY", "display_name": "MYOGLOBIN-SERUM", "hms_code": "000426", "id": 5, "instructions": "", "international_code": "", "interpretation": "", "method": "ECLIA", "min_sample_qty": "", "price": 1200, "primary_specimen": "", "quantity": 1, "reference_range": "25-58", "reporting_days": 0, "result_unit": "", "sample_received": false, "sample_received_timestamp": null, "sample_status": "Not Received", "service_time": "", "short_name": "MYOS", "specimen": "Serum", "suffix_desc": "", "test_done_on": "all", "test_master_data": {"applicableTo": "Both", "container": null, "containerCode": null, "createdAt": "2025-07-09T12:20:55.644672", "criticalHigh": null, "criticalLow": null, "cutoffTime": "", "decimals": 2, "department": "HAEMATOLOGY", "excelSource": true, "hmsCode": "000426", "id": 48, "instructions": null, "isActive": true, "method": "ECLIA", "methodCode": 52, "minSampleQty": null, "notes": null, "price": 1200, "referenceRange": "25-58", "reportingDays": 0, "resultType": "Numeric", "resultUnit": "ug/L", "serviceTime": "", "shortName": "MYOS", "sourceSheet": "HAEMATOLOGY", "specimen": "Serum", "specimenCode": 64, "testCode": "000426", "testDoneOn": "all", "testName": "MYOGLOBIN-SERUM", "testPrice": 1200, "testProfile": "MYOGLOBIN-SERUM", "updatedAt": "2025-07-09T12:20:55.644675"}, "test_master_id": 0, "test_name": "MYOGLOBIN-SERUM", "test_price": null, "test_suffix": "", "unacceptable_conditions": []}, {"amount": 20, "applicable_to": "Both", "container": null, "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 1, "department": "HAEMATOLOGY", "display_name": "CLOTTING TIME", "hms_code": "000347", "id": 6, "instructions": "", "international_code": "", "interpretation": "", "method": "<PERSON> and <PERSON> method", "min_sample_qty": "", "price": 20, "primary_specimen": "", "quantity": 1, "reference_range": "5 - 10", "reporting_days": 0, "result_unit": "", "sample_received": false, "sample_received_timestamp": null, "sample_status": "Not Received", "service_time": "", "short_name": "", "specimen": "", "suffix_desc": "", "test_done_on": "all", "test_master_data": {"applicableTo": "Both", "container": null, "containerCode": null, "createdAt": "2025-07-09T12:20:55.639584", "criticalHigh": null, "criticalLow": null, "cutoffTime": "", "decimals": 0, "department": "HAEMATOLOGY", "excelSource": true, "hmsCode": "000347", "id": 13, "instructions": null, "isActive": true, "method": "<PERSON> and <PERSON> method", "methodCode": 106, "minSampleQty": null, "notes": null, "price": 20, "referenceRange": "5 - 10", "reportingDays": 0, "resultType": "Pick List", "resultUnit": "minutes", "serviceTime": "", "shortName": null, "sourceSheet": "HAEMATOLOGY", "specimen": null, "specimenCode": null, "testCode": "000347", "testDoneOn": "all", "testName": "CLOTTING TIME", "testPrice": 20, "testProfile": "CLOTTING TIME", "updatedAt": "2025-07-09T12:20:55.639586"}, "test_master_id": 0, "test_name": "CLOTTING TIME", "test_price": null, "test_suffix": "", "unacceptable_conditions": []}, {"amount": 400, "applicable_to": "Both", "container": null, "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 1, "department": "HAEMATOLOGY", "display_name": "Malarial Parasite (QBC)", "hms_code": "000427", "id": 7, "instructions": "", "international_code": "", "interpretation": "", "method": "QBC", "min_sample_qty": "", "price": 400, "primary_specimen": "", "quantity": 1, "reference_range": "< 1    Parasite   : + 1-10   Parasites  : ++ 11-100 Parasites  : +++ > 100  Parasites  : +++", "reporting_days": 0, "result_unit": "", "sample_received": false, "sample_received_timestamp": null, "sample_status": "Not Received", "service_time": "", "short_name": "", "specimen": "", "suffix_desc": "", "test_done_on": "all", "test_master_data": {"applicableTo": "Both", "container": null, "containerCode": null, "createdAt": "2025-07-09T12:20:55.643757", "criticalHigh": null, "criticalLow": null, "cutoffTime": "", "decimals": 0, "department": "HAEMATOLOGY", "excelSource": true, "hmsCode": "000427", "id": 40, "instructions": null, "isActive": true, "method": "QBC", "methodCode": 122, "minSampleQty": null, "notes": null, "price": 400, "referenceRange": "< 1    Parasite   : + 1-10   Parasites  : ++ 11-100 Parasites  : +++ > 100  Parasites  : +++", "reportingDays": 0, "resultType": "Pick List", "resultUnit": "per QBC Field", "serviceTime": "", "shortName": null, "sourceSheet": "HAEMATOLOGY", "specimen": null, "specimenCode": null, "testCode": "000427", "testDoneOn": "all", "testName": "Malarial Parasite (QBC)", "testPrice": 400, "testProfile": "Malarial Parasite (QBC)", "updatedAt": "2025-07-09T12:20:55.643760"}, "test_master_id": 0, "test_name": "Malarial Parasite (QBC)", "test_price": null, "test_suffix": "", "unacceptable_conditions": []}, {"amount": 100, "applicable_to": "Both", "container": null, "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 1, "department": "HAEMATOLOGY", "display_name": "Haemoglobin", "hms_code": "000361", "id": 8, "instructions": "", "international_code": "", "interpretation": "", "method": "Non-Cyanide Haemoglobin Analysis", "min_sample_qty": "", "price": 100, "primary_specimen": "", "quantity": 1, "reference_range": "12.0 - 16.0\n14.0 - 18.0", "reporting_days": 0, "result_unit": "", "sample_received": false, "sample_received_timestamp": null, "sample_status": "Not Received", "service_time": "", "short_name": "HB", "specimen": "EDTA BLOOD", "suffix_desc": "", "test_done_on": "all", "test_master_data": {"applicableTo": "Both", "container": "EDTA Container", "containerCode": 7, "createdAt": "2025-07-09T12:20:55.641620", "criticalHigh": 19, "criticalLow": 7, "cutoffTime": "", "decimals": 1, "department": "HAEMATOLOGY", "excelSource": true, "hmsCode": "000361", "id": 28, "instructions": null, "isActive": true, "method": "Non-Cyanide Haemoglobin Analysis", "methodCode": 12, "minSampleQty": null, "notes": null, "price": 100, "referenceRange": "12.0 - 16.0\n14.0 - 18.0", "reportingDays": 0, "resultType": "-", "resultUnit": "g/dL", "serviceTime": "", "shortName": "HB", "sourceSheet": "HAEMATOLOGY", "specimen": "EDTA BLOOD", "specimenCode": 17, "testCode": "000361", "testDoneOn": "all", "testName": "Haemoglobin", "testPrice": 100, "testProfile": "Haemoglobin", "updatedAt": "2025-07-09T12:20:55.641625"}, "test_master_id": 0, "test_name": "Haemoglobin", "test_price": null, "test_suffix": "", "unacceptable_conditions": []}], "tax_amount": 1447.2, "net_amount": 9487.2, "patient": {"first_name": "Tamil", "id": 75, "last_name": "#75"}}, {"id": 82, "invoice_number": "INV00082", "sid_number": "447", "patient_id": 76, "items": [{"amount": 3100, "applicable_to": "Both", "container": "", "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 1, "department": "BIOCHEMISTRY", "display_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CO)", "hms_code": "000260", "id": 1754915313859, "instructions": "", "international_code": "", "interpretation": "", "method": "LC-MS/MS", "min_sample_qty": "", "price": 3100, "primary_specimen": "EDTA BLOOD", "quantity": 1, "reference_range": "", "reporting_days": 0, "result_unit": "ug/L", "service_time": "", "short_name": "", "specimen": "EDTA BLOOD", "suffix_desc": "", "testName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CO)", "test_done_on": "all", "test_id": 463, "test_master_data": {"applicableTo": "Both", "applicable_to": "Both", "container": null, "container_code": null, "created_at": "2025-07-09T12:20:56.537407", "criticalHigh": null, "criticalLow": null, "critical_high": null, "critical_low": null, "cutoffTime": "", "decimals": 1, "department": "BIOCHEMISTRY", "excel_source": true, "hmsCode": "000260", "id": 463, "instructions": null, "isActive": true, "is_active": true, "method": "LC-MS/MS", "method_code": 105, "minSampleQty": "", "min_sample_qty": null, "notes": null, "price": 3100, "referenceRange": "", "reference_range": null, "reportingDays": "", "reporting_days": 0, "resultUnit": "ug/L", "result_type": "-", "result_unit": "ug/L", "serviceTime": "", "short_name": null, "source_sheet": "BioChemistry", "specimen": "EDTA BLOOD", "specimen_code": 17, "testDoneOn": "all", "testName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CO)", "test_code": "000260", "test_done_on": "all", "test_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CO)", "test_price": 3100, "test_profile": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CO)", "updated_at": "2025-07-09T12:20:56.537410"}, "test_master_id": 463, "test_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> (CO)", "test_price": 3100, "test_suffix": "", "unacceptable_conditions": ""}, {"amount": 20, "description": "CLOTTING TIME", "display_name": "CLOTTING TIME", "id": 1754915384937, "price": 20, "quantity": 1, "status": "Pending", "test_id": 13, "test_master_data": {"applicableTo": "Both", "container": null, "containerCode": null, "createdAt": "2025-07-09T12:20:55.639584", "criticalHigh": null, "criticalLow": null, "cutoffTime": "", "decimals": 0, "department": "HAEMATOLOGY", "excelSource": true, "hmsCode": "000347", "id": 13, "instructions": null, "isActive": true, "method": "<PERSON> and <PERSON> method", "methodCode": 106, "minSampleQty": null, "notes": null, "price": 20, "referenceRange": "5 - 10", "reportingDays": 0, "resultType": "Pick List", "resultUnit": "minutes", "serviceTime": "", "shortName": null, "sourceSheet": "HAEMATOLOGY", "specimen": null, "specimenCode": null, "testCode": "000347", "testDoneOn": "all", "testName": "CLOTTING TIME", "testPrice": 20, "testProfile": "CLOTTING TIME", "updatedAt": "2025-07-09T12:20:55.639586"}, "test_name": "CLOTTING TIME", "total": 20, "unit_price": 20}], "bill_amount": 3100, "other_charges": 0, "discount_percent": 0, "subtotal": 3100, "discount": 0, "gst_rate": 18, "gst_amount": 558, "tax": 558, "total_amount": 209.0, "paid_amount": 3100, "balance": -3076.4, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-11", "due_date": "2025-09-10", "notes": "", "branch": 1, "created_at": "2025-08-11T17:58:53.235666", "updated_at": "2025-08-11T17:59:44.936306", "tenant_id": 1, "created_by": 4, "test_items": [{"amount": 20, "applicable_to": "Both", "container": null, "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 1, "department": "HAEMATOLOGY", "display_name": "CLOTTING TIME", "hms_code": "000347", "id": 1, "instructions": "", "international_code": "", "interpretation": "", "method": "<PERSON> and <PERSON> method", "min_sample_qty": "", "price": 20, "primary_specimen": "", "quantity": 1, "reference_range": "5 - 10", "reporting_days": 0, "result_unit": "", "sample_received": false, "sample_received_timestamp": null, "sample_status": "Not Received", "service_time": "", "short_name": "", "specimen": "", "suffix_desc": "", "test_done_on": "all", "test_master_data": {"applicableTo": "Both", "container": null, "containerCode": null, "createdAt": "2025-07-09T12:20:55.639584", "criticalHigh": null, "criticalLow": null, "cutoffTime": "", "decimals": 0, "department": "HAEMATOLOGY", "excelSource": true, "hmsCode": "000347", "id": 13, "instructions": null, "isActive": true, "method": "<PERSON> and <PERSON> method", "methodCode": 106, "minSampleQty": null, "notes": null, "price": 20, "referenceRange": "5 - 10", "reportingDays": 0, "resultType": "Pick List", "resultUnit": "minutes", "serviceTime": "", "shortName": null, "sourceSheet": "HAEMATOLOGY", "specimen": null, "specimenCode": null, "testCode": "000347", "testDoneOn": "all", "testName": "CLOTTING TIME", "testPrice": 20, "testProfile": "CLOTTING TIME", "updatedAt": "2025-07-09T12:20:55.639586"}, "test_master_id": 0, "test_name": "CLOTTING TIME", "test_price": null, "test_suffix": "", "unacceptable_conditions": []}], "tax_amount": 3.5999999999999996, "net_amount": 23.6, "patient": {"first_name": "divakarR", "id": 76, "last_name": "r"}}, {"id": 83, "invoice_number": "INV00083", "sid_number": "449", "patient_id": 77, "items": [{"id": 1754986298665, "test_id": 40, "test_master_id": 40, "testName": "Malarial Parasite (QBC)", "test_name": "Malarial Parasite (QBC)", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000427", "display_name": "Malarial Parasite (QBC)", "short_name": "", "international_code": "", "method": "QBC", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "< 1    Parasite   : + 1-10   Parasites  : ++ 11-100 Parasites  : +++ > 100  Parasites  : +++", "result_unit": "per QBC Field", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 40, "testName": "Malarial Parasite (QBC)", "test_profile": "Malarial Parasite (QBC)", "test_price": 400, "department": "HAEMATOLOGY", "hmsCode": "000427", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "< 1    Parasite   : + 1-10   Parasites  : ++ 11-100 Parasites  : +++ > 100  Parasites  : +++", "resultUnit": "per QBC Field", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "QBC", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.643757", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 122, "min_sample_qty": null, "price": 400, "reference_range": "< 1    Parasite   : + 1-10   Parasites  : ++ 11-100 Parasites  : +++ > 100  Parasites  : +++", "reporting_days": 0, "result_type": "Pick List", "result_unit": "per QBC Field", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000427", "test_done_on": "all", "test_name": "Malarial Parasite (QBC)", "updated_at": "2025-07-09T12:20:55.643760"}}, {"id": 1754986308130, "test_id": 46, "test_master_id": 46, "testName": "MICROFILARIA (MF)  by QBC", "test_name": "MICROFILARIA (MF)  by QBC", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000431", "display_name": "MICROFILARIA (MF)  by QBC", "short_name": "QBC", "international_code": "", "method": "QBC", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "", "reference_range": "( QBC )", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 46, "testName": "MICROFILARIA (MF)  by QBC", "test_profile": "MICROFILARIA (MF)  by QBC", "test_price": 400, "department": "HAEMATOLOGY", "hmsCode": "000431", "specimen": "EDTA BLOOD", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "( QBC )", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "QBC", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.644445", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 122, "min_sample_qty": null, "price": 400, "reference_range": "( QBC )", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "QBC", "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "000431", "test_done_on": "all", "test_name": "MICROFILARIA (MF)  by QBC", "updated_at": "2025-07-09T12:20:55.644448"}}, {"id": 1754986315419, "test_id": 13, "test_master_id": 13, "testName": "CLOTTING TIME", "test_name": "CLOTTING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000347", "display_name": "CLOTTING TIME", "short_name": "", "international_code": "", "method": "<PERSON> and <PERSON> method", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "5 - 10", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 13, "testName": "CLOTTING TIME", "test_profile": "CLOTTING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000347", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "5 - 10", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "<PERSON> and <PERSON> method", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639584", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 106, "min_sample_qty": null, "price": 20, "reference_range": "5 - 10", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000347", "test_done_on": "all", "test_name": "CLOTTING TIME", "updated_at": "2025-07-09T12:20:55.639586"}}, {"amount": 1500, "description": "Lupus Anticoagulant (dRVVT)", "display_name": "Lupus Anticoagulant (dRVVT)", "id": 1754986847293, "price": 1500, "quantity": 1, "test_id": 36, "test_name": "Lupus Anticoagulant (dRVVT)", "unit_price": 1500, "total": 1500, "status": "Pending", "test_master_data": {"id": 36, "testName": "Lupus Anticoagulant (dRVVT)", "testProfile": "Lupus Anticoagulant (dRVVT)", "testPrice": 1500, "department": "HAEMATOLOGY", "hmsCode": "000326", "specimen": "Citrate Plasma", "container": "Citrate Container", "serviceTime": "", "reportingDays": 0, "cutoffTime": "", "referenceRange": null, "resultUnit": null, "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "COAGULATION", "instructions": null, "notes": null, "minSampleQty": null, "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "containerCode": 8, "createdAt": "2025-07-09T12:20:55.643268", "excelSource": true, "methodCode": 30, "price": 1500, "resultType": "Pick List", "shortName": "LAC", "sourceSheet": "HAEMATOLOGY", "specimenCode": 14, "testCode": "000326", "updatedAt": "2025-07-09T12:20:55.643272"}}], "bill_amount": 820, "other_charges": 0, "discount_percent": 0, "subtotal": 820, "discount": 0, "gst_rate": 18, "gst_amount": 147.6, "tax": 147.6, "total_amount": 1500, "paid_amount": 820, "balance": 950.0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-12", "due_date": "2025-09-11", "notes": "", "branch": 1, "created_at": "2025-08-12T13:43:27.999154", "updated_at": "2025-08-12T13:50:47.292314", "tenant_id": 1, "created_by": 4, "test_items": [{"id": 1, "amount": 1500, "applicable_to": "Both", "container": null, "critical_high": null, "critical_low": null, "cutoff_time": "", "decimals": 1, "department": "HAEMATOLOGY", "display_name": "Lupus Anticoagulant (dRVVT)", "hms_code": "000326", "instructions": "", "international_code": "", "interpretation": "", "method": "COAGULATION", "min_sample_qty": "", "price": 1500, "primary_specimen": "", "quantity": 1, "reference_range": "", "reporting_days": 0, "result_unit": "", "sample_received": false, "sample_received_timestamp": null, "sample_status": "Not Received", "service_time": "", "short_name": "LAC", "specimen": "Citrate Plasma", "suffix_desc": "", "test_done_on": "all", "test_master_data": {"id": 36, "testName": "Lupus Anticoagulant (dRVVT)", "testProfile": "Lupus Anticoagulant (dRVVT)", "testPrice": 1500, "department": "HAEMATOLOGY", "hmsCode": "000326", "specimen": "Citrate Plasma", "container": "Citrate Container", "serviceTime": "", "reportingDays": 0, "cutoffTime": "", "referenceRange": null, "resultUnit": null, "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "COAGULATION", "instructions": null, "notes": null, "minSampleQty": null, "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "containerCode": 8, "createdAt": "2025-07-09T12:20:55.643268", "excelSource": true, "methodCode": 30, "price": 1500, "resultType": "Pick List", "shortName": "LAC", "sourceSheet": "HAEMATOLOGY", "specimenCode": 14, "testCode": "000326", "updatedAt": "2025-07-09T12:20:55.643272"}, "test_master_id": 0, "test_name": "Lupus Anticoagulant (dRVVT)", "test_price": null, "test_suffix": "", "unacceptable_conditions": []}], "tax_amount": 270.0, "net_amount": 1770.0}, {"id": 84, "invoice_number": "INV00084", "sid_number": "453", "patient_id": 78, "items": [{"id": 1755088027493, "test_id": 442, "test_master_id": 442, "testName": "Cholesterol, HDL", "test_name": "Cholesterol, HDL", "amount": 150, "price": 150, "test_price": 150, "quantity": 1, "department": "BIOCHEMISTRY", "hms_code": "000087", "display_name": "Cholesterol, HDL", "short_name": "HDL", "international_code": "", "method": "Direct", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "Low  : < 40 High : >=60", "result_unit": "mg/dL", "decimals": 1, "critical_low": 15, "critical_high": 80, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 442, "testName": "Cholesterol, HDL", "test_profile": "Cholesterol, HDL", "test_price": 150, "department": "BIOCHEMISTRY", "hmsCode": "000087", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Low  : < 40 High : >=60", "resultUnit": "mg/dL", "decimals": 1, "criticalLow": 15, "criticalHigh": 80, "method": "Direct", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.535221", "critical_high": 80, "critical_low": 15, "excel_source": true, "is_active": true, "method_code": 49, "min_sample_qty": null, "price": 150, "reference_range": "Low  : < 40 High : >=60", "reporting_days": 0, "result_type": "-", "result_unit": "mg/dL", "short_name": "HDL", "source_sheet": "BioChemistry", "specimen_code": 64, "test_code": "000087", "test_done_on": "all", "test_name": "Cholesterol, HDL", "updated_at": "2025-07-09T12:20:56.535224"}}, {"id": 1755088038311, "test_id": 588, "test_master_id": 588, "testName": "Urea", "test_name": "Urea", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "BIOCHEMISTRY", "hms_code": "000214", "display_name": "Urea", "short_name": "<PERSON><PERSON>", "international_code": "", "method": "Urease/GLDH", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "", "result_unit": "mg/dL", "decimals": 2, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 588, "testName": "Urea", "test_profile": "Urea", "test_price": 100, "department": "BIOCHEMISTRY", "hmsCode": "000214", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "mg/dL", "decimals": 2, "criticalLow": null, "criticalHigh": null, "method": "Urease/GLDH", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.555920", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 165, "min_sample_qty": null, "price": 100, "reference_range": null, "reporting_days": 0, "result_type": "Numeric", "result_unit": "mg/dL", "short_name": "<PERSON><PERSON>", "source_sheet": "BioChemistry", "specimen_code": 64, "test_code": "000214", "test_done_on": "all", "test_name": "Urea", "updated_at": "2025-07-09T12:20:56.555922"}}], "bill_amount": 250, "other_charges": 0, "discount_percent": 0, "subtotal": 250, "discount": 0, "gst_rate": 18, "gst_amount": 45, "tax": 45, "total_amount": 250, "paid_amount": 250, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-13", "due_date": "2025-09-12", "notes": "", "branch": "1", "created_at": "2025-08-13T17:59:58.596594", "updated_at": "2025-08-13T17:59:58.596597", "tenant_id": 1, "created_by": 4}, {"id": 85, "invoice_number": "INV00085", "sid_number": "455", "patient_id": 79, "items": [{"id": 1755090453820, "test_id": 10, "test_master_id": 10, "testName": "BLEEDING TIME", "test_name": "BLEEDING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000342", "display_name": "BLEEDING TIME", "short_name": "BT", "international_code": "", "method": "IVY", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "1 to 6", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 10, "testName": "BLEEDING TIME", "test_profile": "BLEEDING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000342", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "1 to 6", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IVY", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639261", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 100, "min_sample_qty": null, "price": 20, "reference_range": "1 to 6", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": "BT", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000342", "test_done_on": "all", "test_name": "BLEEDING TIME", "updated_at": "2025-07-09T12:20:55.639264"}}, {"id": 1755090503092, "test_id": 214, "test_master_id": 214, "testName": "ANTI HIV 1&2 (CARD)", "test_name": "ANTI HIV 1&2 (CARD)", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "SEROLOGY", "hms_code": "001071", "display_name": "ANTI HIV 1&2 (CARD)", "short_name": "HIV CARD", "international_code": "", "method": "IC", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "Non Reactive", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 214, "testName": "ANTI HIV 1&2 (CARD)", "test_profile": "ANTI HIV 1&2 (CARD)", "test_price": 400, "department": "SEROLOGY", "hmsCode": "001071", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Non Reactive", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IC", "instructions": null, "notes": "Note:\n     HIV-1/2 is an in-vitro, visually read, qualitative immunoassay for the detection of antibodies to HIV-1 and HIV-2 in human serum, plasma or whole blood. The test is intended as an aid to detect the antibodies to HIV-1/2 from infected individuals. HIV-1/2 test in other body fluids or pooled specimens may not give accurate results.\n\n     A negative result with HIV-1/2 does not exclude the possibility of infection with HIV. A false negative results can occur in the following circumstances 1. Low level of antibody (eg. Early seroconversion specimens) are below the detection limits of the test. 2. Infection with a variant of the virus that is less detectable. 3. HIV antibodies in the patient that don’t react with specific antigens utilized in the assay configuration. 4. HIV-infected persons taking anti-retroviral medication. For these reasons, care should be taken in interpreting negative results. Other clinical data such as symptom or risk factors should be used in conjugation with test results.\n\n     Positive specimens should be retested using another method and the results should be evaluated in light of the overall clinical evaluation before a diagnosis. - Whole blood or plasma specimen containing other than EDTA may give incorrect results.\n\n     Neonates of HIV-infected mothers may carry maternal antibodies to HIV for upto around 18 months, which may not necessarily indicate the true infection status of the newborn.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.165858", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 80, "min_sample_qty": null, "price": 400, "reference_range": "Non Reactive", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "HIV CARD", "source_sheet": "Serology", "specimen_code": 64, "test_code": "001071", "test_done_on": "all", "test_name": "ANTI HIV 1&2 (CARD)", "updated_at": "2025-07-09T12:20:56.165861"}}, {"id": 1755090519768, "test_id": 296, "test_master_id": 296, "testName": "HBsAg (CARD)", "test_name": "HBsAg (CARD)", "amount": 200, "price": 200, "test_price": 200, "quantity": 1, "department": "SEROLOGY", "hms_code": "001128", "display_name": "HBsAg (CARD)", "short_name": "HbsAg CARD", "international_code": "", "method": "IC", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "NEGATIVE", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 296, "testName": "HBsAg (CARD)", "test_profile": "HBsAg (CARD)", "test_price": 200, "department": "SEROLOGY", "hmsCode": "001128", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NEGATIVE", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IC", "instructions": null, "notes": "Note\n\n HBsAg test is an in-vitro immunochromatographic, one step assay designed for qualitative determination of HBsAg in human serum or plasma.\n\n\nA negative result does not preclude the possibility of infection with Hepatitis B Virus.\n\nOther clinically available tests (plz mention) are required if questionable results are obtained.\n\nAs with all diagnostic tests a definitive clinical diagnosis should not be based on the result of a single test, but should only be made by the physician after all clinical and laboratory findings have been evaluated.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.175290", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 80, "min_sample_qty": null, "price": 200, "reference_range": "NEGATIVE", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "HbsAg CARD", "source_sheet": "Serology", "specimen_code": 64, "test_code": "001128", "test_done_on": "all", "test_name": "HBsAg (CARD)", "updated_at": "2025-07-09T12:20:56.175293"}}, {"id": 1755090540759, "test_id": 13, "test_master_id": 13, "testName": "CLOTTING TIME", "test_name": "CLOTTING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000347", "display_name": "CLOTTING TIME", "short_name": "", "international_code": "", "method": "<PERSON> and <PERSON> method", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "5 - 10", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 13, "testName": "CLOTTING TIME", "test_profile": "CLOTTING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000347", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "5 - 10", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "<PERSON> and <PERSON> method", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639584", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 106, "min_sample_qty": null, "price": 20, "reference_range": "5 - 10", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000347", "test_done_on": "all", "test_name": "CLOTTING TIME", "updated_at": "2025-07-09T12:20:55.639586"}}], "bill_amount": 640, "other_charges": 0, "discount_percent": 0, "subtotal": 640, "discount": 0, "gst_rate": 18, "gst_amount": 115.2, "tax": 115.2, "total_amount": 640, "paid_amount": 640, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-13", "due_date": "2025-09-12", "notes": "", "branch": 1, "created_at": "2025-08-13T18:39:54.964183", "updated_at": "2025-08-13T18:39:54.964187", "tenant_id": 1, "created_by": 4}, {"id": 86, "invoice_number": "INV00086", "sid_number": "457", "patient_id": 80, "items": [{"id": 1755159932459, "test_id": 374, "test_master_id": 374, "testName": "Toxoplasma, Avidity test", "test_name": "Toxoplasma, Avidity test", "amount": 1500, "price": 1500, "test_price": 1500, "quantity": 1, "department": "SEROLOGY", "hms_code": "001271", "display_name": "Toxoplasma, Avidity test", "short_name": "TOXA", "international_code": "", "method": "EIA", "primary_specimen": "SERUM", "specimen": "SERUM", "container": "Serum Container", "reference_range": "Low Avidity      : <30  Grayzone Avidity : 30 - 35  High Avidity     : >35", "result_unit": "%", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 374, "testName": "Toxoplasma, Avidity test", "test_profile": "Toxoplasma, Avidity test", "test_price": 1500, "department": "SEROLOGY", "hmsCode": "001271", "specimen": "SERUM", "container": "Serum Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Low Avidity      : <30  Grayzone Avidity : 30 - 35  High Avidity     : >35", "resultUnit": "%", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "EIA", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 4, "created_at": "2025-07-09T12:20:56.187427", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 53, "min_sample_qty": null, "price": 1500, "reference_range": "Low Avidity      : <30  Grayzone Avidity : 30 - 35  High Avidity     : >35", "reporting_days": 0, "result_type": "Pick List", "result_unit": "%", "short_name": "TOXA", "source_sheet": "Serology", "specimen_code": 39, "test_code": "001271", "test_done_on": "all", "test_name": "Toxoplasma, Avidity test", "updated_at": "2025-07-09T12:20:56.187430"}}, {"id": 1755159937895, "test_id": 13, "test_master_id": 13, "testName": "CLOTTING TIME", "test_name": "CLOTTING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000347", "display_name": "CLOTTING TIME", "short_name": "", "international_code": "", "method": "<PERSON> and <PERSON> method", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "5 - 10", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 13, "testName": "CLOTTING TIME", "test_profile": "CLOTTING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000347", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "5 - 10", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "<PERSON> and <PERSON> method", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639584", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 106, "min_sample_qty": null, "price": 20, "reference_range": "5 - 10", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000347", "test_done_on": "all", "test_name": "CLOTTING TIME", "updated_at": "2025-07-09T12:20:55.639586"}}], "bill_amount": 1520, "other_charges": 0, "discount_percent": 0, "subtotal": 1520, "discount": 0, "gst_rate": 18, "gst_amount": 273.6, "tax": 273.6, "total_amount": 1520, "paid_amount": 1520, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-14", "due_date": "2025-09-13", "notes": "", "branch": 1, "created_at": "2025-08-14T13:56:27.925822", "updated_at": "2025-08-14T13:56:27.925823", "tenant_id": 1, "created_by": 4}, {"id": 87, "invoice_number": "INV00087", "sid_number": "460", "patient_id": 81, "items": [{"id": 1755165214468, "test_id": 10, "test_master_id": 10, "testName": "BLEEDING TIME", "test_name": "BLEEDING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000342", "display_name": "BLEEDING TIME", "short_name": "BT", "international_code": "", "method": "IVY", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "1 to 6", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 10, "testName": "BLEEDING TIME", "test_profile": "BLEEDING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000342", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "1 to 6", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IVY", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639261", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 100, "min_sample_qty": null, "price": 20, "reference_range": "1 to 6", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": "BT", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000342", "test_done_on": "all", "test_name": "BLEEDING TIME", "updated_at": "2025-07-09T12:20:55.639264"}}, {"id": 1755165222573, "test_id": 13, "test_master_id": 13, "testName": "CLOTTING TIME", "test_name": "CLOTTING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000347", "display_name": "CLOTTING TIME", "short_name": "", "international_code": "", "method": "<PERSON> and <PERSON> method", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "5 - 10", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 13, "testName": "CLOTTING TIME", "test_profile": "CLOTTING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000347", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "5 - 10", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "<PERSON> and <PERSON> method", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639584", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 106, "min_sample_qty": null, "price": 20, "reference_range": "5 - 10", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000347", "test_done_on": "all", "test_name": "CLOTTING TIME", "updated_at": "2025-07-09T12:20:55.639586"}}, {"id": 1755165236003, "test_id": 214, "test_master_id": 214, "testName": "ANTI HIV 1&2 (CARD)", "test_name": "ANTI HIV 1&2 (CARD)", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "SEROLOGY", "hms_code": "001071", "display_name": "ANTI HIV 1&2 (CARD)", "short_name": "HIV CARD", "international_code": "", "method": "IC", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "Non Reactive", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 214, "testName": "ANTI HIV 1&2 (CARD)", "test_profile": "ANTI HIV 1&2 (CARD)", "test_price": 400, "department": "SEROLOGY", "hmsCode": "001071", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Non Reactive", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IC", "instructions": null, "notes": "Note:\n     HIV-1/2 is an in-vitro, visually read, qualitative immunoassay for the detection of antibodies to HIV-1 and HIV-2 in human serum, plasma or whole blood. The test is intended as an aid to detect the antibodies to HIV-1/2 from infected individuals. HIV-1/2 test in other body fluids or pooled specimens may not give accurate results.\n\n     A negative result with HIV-1/2 does not exclude the possibility of infection with HIV. A false negative results can occur in the following circumstances 1. Low level of antibody (eg. Early seroconversion specimens) are below the detection limits of the test. 2. Infection with a variant of the virus that is less detectable. 3. HIV antibodies in the patient that don’t react with specific antigens utilized in the assay configuration. 4. HIV-infected persons taking anti-retroviral medication. For these reasons, care should be taken in interpreting negative results. Other clinical data such as symptom or risk factors should be used in conjugation with test results.\n\n     Positive specimens should be retested using another method and the results should be evaluated in light of the overall clinical evaluation before a diagnosis. - Whole blood or plasma specimen containing other than EDTA may give incorrect results.\n\n     Neonates of HIV-infected mothers may carry maternal antibodies to HIV for upto around 18 months, which may not necessarily indicate the true infection status of the newborn.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.165858", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 80, "min_sample_qty": null, "price": 400, "reference_range": "Non Reactive", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "HIV CARD", "source_sheet": "Serology", "specimen_code": 64, "test_code": "001071", "test_done_on": "all", "test_name": "ANTI HIV 1&2 (CARD)", "updated_at": "2025-07-09T12:20:56.165861"}}, {"id": 1755165245356, "test_id": 296, "test_master_id": 296, "testName": "HBsAg (CARD)", "test_name": "HBsAg (CARD)", "amount": 200, "price": 200, "test_price": 200, "quantity": 1, "department": "SEROLOGY", "hms_code": "001128", "display_name": "HBsAg (CARD)", "short_name": "HbsAg CARD", "international_code": "", "method": "IC", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "NEGATIVE", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 296, "testName": "HBsAg (CARD)", "test_profile": "HBsAg (CARD)", "test_price": 200, "department": "SEROLOGY", "hmsCode": "001128", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NEGATIVE", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IC", "instructions": null, "notes": "Note\n\n HBsAg test is an in-vitro immunochromatographic, one step assay designed for qualitative determination of HBsAg in human serum or plasma.\n\n\nA negative result does not preclude the possibility of infection with Hepatitis B Virus.\n\nOther clinically available tests (plz mention) are required if questionable results are obtained.\n\nAs with all diagnostic tests a definitive clinical diagnosis should not be based on the result of a single test, but should only be made by the physician after all clinical and laboratory findings have been evaluated.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.175290", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 80, "min_sample_qty": null, "price": 200, "reference_range": "NEGATIVE", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "HbsAg CARD", "source_sheet": "Serology", "specimen_code": 64, "test_code": "001128", "test_done_on": "all", "test_name": "HBsAg (CARD)", "updated_at": "2025-07-09T12:20:56.175293"}}], "bill_amount": 640, "other_charges": 0, "discount_percent": 0, "subtotal": 640, "discount": 0, "gst_rate": 18, "gst_amount": 115.2, "tax": 115.2, "total_amount": 640, "paid_amount": 640, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-14", "due_date": "2025-09-13", "notes": "", "branch": 1, "created_at": "2025-08-14T15:24:44.055555", "updated_at": "2025-08-14T15:24:44.055556", "tenant_id": 1, "created_by": 4}, {"id": 88, "invoice_number": "INV00088", "sid_number": "462", "patient_id": 82, "items": [{"id": 1755165536995, "test_id": 10, "test_master_id": 10, "testName": "BLEEDING TIME", "test_name": "BLEEDING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000342", "display_name": "BLEEDING TIME", "short_name": "BT", "international_code": "", "method": "IVY", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "1 to 6", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 10, "testName": "BLEEDING TIME", "test_profile": "BLEEDING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000342", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "1 to 6", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IVY", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639261", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 100, "min_sample_qty": null, "price": 20, "reference_range": "1 to 6", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": "BT", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000342", "test_done_on": "all", "test_name": "BLEEDING TIME", "updated_at": "2025-07-09T12:20:55.639264"}}, {"id": 1755165550091, "test_id": 13, "test_master_id": 13, "testName": "CLOTTING TIME", "test_name": "CLOTTING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000347", "display_name": "CLOTTING TIME", "short_name": "", "international_code": "", "method": "<PERSON> and <PERSON> method", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "5 - 10", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 13, "testName": "CLOTTING TIME", "test_profile": "CLOTTING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000347", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "5 - 10", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "<PERSON> and <PERSON> method", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639584", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 106, "min_sample_qty": null, "price": 20, "reference_range": "5 - 10", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000347", "test_done_on": "all", "test_name": "CLOTTING TIME", "updated_at": "2025-07-09T12:20:55.639586"}}, {"id": 1755165562312, "test_id": 214, "test_master_id": 214, "testName": "ANTI HIV 1&2 (CARD)", "test_name": "ANTI HIV 1&2 (CARD)", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "SEROLOGY", "hms_code": "001071", "display_name": "ANTI HIV 1&2 (CARD)", "short_name": "HIV CARD", "international_code": "", "method": "IC", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "Non Reactive", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 214, "testName": "ANTI HIV 1&2 (CARD)", "test_profile": "ANTI HIV 1&2 (CARD)", "test_price": 400, "department": "SEROLOGY", "hmsCode": "001071", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Non Reactive", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IC", "instructions": null, "notes": "Note:\n     HIV-1/2 is an in-vitro, visually read, qualitative immunoassay for the detection of antibodies to HIV-1 and HIV-2 in human serum, plasma or whole blood. The test is intended as an aid to detect the antibodies to HIV-1/2 from infected individuals. HIV-1/2 test in other body fluids or pooled specimens may not give accurate results.\n\n     A negative result with HIV-1/2 does not exclude the possibility of infection with HIV. A false negative results can occur in the following circumstances 1. Low level of antibody (eg. Early seroconversion specimens) are below the detection limits of the test. 2. Infection with a variant of the virus that is less detectable. 3. HIV antibodies in the patient that don’t react with specific antigens utilized in the assay configuration. 4. HIV-infected persons taking anti-retroviral medication. For these reasons, care should be taken in interpreting negative results. Other clinical data such as symptom or risk factors should be used in conjugation with test results.\n\n     Positive specimens should be retested using another method and the results should be evaluated in light of the overall clinical evaluation before a diagnosis. - Whole blood or plasma specimen containing other than EDTA may give incorrect results.\n\n     Neonates of HIV-infected mothers may carry maternal antibodies to HIV for upto around 18 months, which may not necessarily indicate the true infection status of the newborn.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.165858", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 80, "min_sample_qty": null, "price": 400, "reference_range": "Non Reactive", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "HIV CARD", "source_sheet": "Serology", "specimen_code": 64, "test_code": "001071", "test_done_on": "all", "test_name": "ANTI HIV 1&2 (CARD)", "updated_at": "2025-07-09T12:20:56.165861"}}, {"id": 1755165573000, "test_id": 295, "test_master_id": 295, "testName": "HBsAg", "test_name": "HBsAg", "amount": 350, "price": 350, "test_price": 350, "quantity": 1, "department": "SEROLOGY", "hms_code": "001129", "display_name": "HBsAg", "short_name": "HBsAg", "international_code": "", "method": "", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "<1.0 - Non Reactive >=1.0- Reactive", "result_unit": "S/Co", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 295, "testName": "HBsAg", "test_profile": "HBsAg", "test_price": 350, "department": "SEROLOGY", "hmsCode": "001129", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "<1.0 - Non Reactive >=1.0- Reactive", "resultUnit": "S/Co", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": "Hepatitis B surface antigen (HBsAg) is the first serologic marker appearing in the serum at 6 to 16 weeks following exposure to HBV. In acute infection, HBsAg usually disappears in 1 to 2 months after the onset of symptoms. Persistence of HBsAg for more than 6 months in duration indicates development of either a chronic carrier state or chronic HBV infection.      The detection of HBsAg is useful in diagnosis of acute, recent, or chronic hepatitis B infection, determination of chronic hepatitis B infection status, routine serological screening during pregnancy and blood transfusion and evaluate the efficacy of antiviral therapy.    \n\nNote:\n\n Not useful during the \"window period\" of acute hepatitis B virus (HBV) infection (ie, after disappearance of hepatitis B surface antigen [(HBsAg] and prior to appearance of hepatitis B surface antibody [anti-HBs]). Testing for acute HBV infection should also include hepatitis B core IgM antibody (anti-HBc IgM).     Positive screen results need for confirmation testing by neutralization assay and should be interpreted in conjunction with test results of other HBV molecular and serologic markers only (eg, anti-HBs, anti-HBc total, and anti-HBc IgM).  \n\n \n\nDiscrepant results may be observed during pregnancy, patients receiving mouse monoclonal antibodies for diagnosis or therapy & mutant forms of HBsAg Individuals, especially neonates and children, who recently received hepatitis B vaccination may have transient positive HBsAg test results because of the large dose of HBsAg used in the vaccine relative to the individual's body mass.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.175175", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 350, "reference_range": "<1.0 - Non Reactive >=1.0- Reactive", "reporting_days": 0, "result_type": "Pick List", "result_unit": "S/Co", "short_name": "HBsAg", "source_sheet": "Serology", "specimen_code": 64, "test_code": "001129", "test_done_on": "all", "test_name": "HBsAg", "updated_at": "2025-07-09T12:20:56.175178"}}], "bill_amount": 790, "other_charges": 0, "discount_percent": 0, "subtotal": 790, "discount": 0, "gst_rate": 18, "gst_amount": 142.2, "tax": 142.2, "total_amount": 790, "paid_amount": 800, "balance": -10, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-14", "due_date": "2025-09-13", "notes": "", "branch": 1, "created_at": "2025-08-14T15:30:13.105738", "updated_at": "2025-08-14T15:30:13.105741", "tenant_id": 1, "created_by": 4}, {"id": 89, "invoice_number": "INV00089", "sid_number": "466", "patient_id": 83, "items": [{"id": 1755166481797, "test_id": 10, "test_master_id": 10, "testName": "BLEEDING TIME", "test_name": "BLEEDING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000342", "display_name": "BLEEDING TIME", "short_name": "BT", "international_code": "", "method": "IVY", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "1 to 6", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 10, "testName": "BLEEDING TIME", "test_profile": "BLEEDING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000342", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "1 to 6", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IVY", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639261", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 100, "min_sample_qty": null, "price": 20, "reference_range": "1 to 6", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": "BT", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000342", "test_done_on": "all", "test_name": "BLEEDING TIME", "updated_at": "2025-07-09T12:20:55.639264"}}, {"id": 1755166494860, "test_id": 13, "test_master_id": 13, "testName": "CLOTTING TIME", "test_name": "CLOTTING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000347", "display_name": "CLOTTING TIME", "short_name": "", "international_code": "", "method": "<PERSON> and <PERSON> method", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "5 - 10", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 13, "testName": "CLOTTING TIME", "test_profile": "CLOTTING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000347", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "5 - 10", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "<PERSON> and <PERSON> method", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639584", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 106, "min_sample_qty": null, "price": 20, "reference_range": "5 - 10", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000347", "test_done_on": "all", "test_name": "CLOTTING TIME", "updated_at": "2025-07-09T12:20:55.639586"}}, {"id": 1755166524553, "test_id": 214, "test_master_id": 214, "testName": "ANTI HIV 1&2 (CARD)", "test_name": "ANTI HIV 1&2 (CARD)", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "SEROLOGY", "hms_code": "001071", "display_name": "ANTI HIV 1&2 (CARD)", "short_name": "HIV CARD", "international_code": "", "method": "IC", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "Non Reactive", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 214, "testName": "ANTI HIV 1&2 (CARD)", "test_profile": "ANTI HIV 1&2 (CARD)", "test_price": 400, "department": "SEROLOGY", "hmsCode": "001071", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Non Reactive", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IC", "instructions": null, "notes": "Note:\n     HIV-1/2 is an in-vitro, visually read, qualitative immunoassay for the detection of antibodies to HIV-1 and HIV-2 in human serum, plasma or whole blood. The test is intended as an aid to detect the antibodies to HIV-1/2 from infected individuals. HIV-1/2 test in other body fluids or pooled specimens may not give accurate results.\n\n     A negative result with HIV-1/2 does not exclude the possibility of infection with HIV. A false negative results can occur in the following circumstances 1. Low level of antibody (eg. Early seroconversion specimens) are below the detection limits of the test. 2. Infection with a variant of the virus that is less detectable. 3. HIV antibodies in the patient that don’t react with specific antigens utilized in the assay configuration. 4. HIV-infected persons taking anti-retroviral medication. For these reasons, care should be taken in interpreting negative results. Other clinical data such as symptom or risk factors should be used in conjugation with test results.\n\n     Positive specimens should be retested using another method and the results should be evaluated in light of the overall clinical evaluation before a diagnosis. - Whole blood or plasma specimen containing other than EDTA may give incorrect results.\n\n     Neonates of HIV-infected mothers may carry maternal antibodies to HIV for upto around 18 months, which may not necessarily indicate the true infection status of the newborn.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.165858", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 80, "min_sample_qty": null, "price": 400, "reference_range": "Non Reactive", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "HIV CARD", "source_sheet": "Serology", "specimen_code": 64, "test_code": "001071", "test_done_on": "all", "test_name": "ANTI HIV 1&2 (CARD)", "updated_at": "2025-07-09T12:20:56.165861"}}, {"id": 1755166536767, "test_id": 296, "test_master_id": 296, "testName": "HBsAg (CARD)", "test_name": "HBsAg (CARD)", "amount": 200, "price": 200, "test_price": 200, "quantity": 1, "department": "SEROLOGY", "hms_code": "001128", "display_name": "HBsAg (CARD)", "short_name": "HbsAg CARD", "international_code": "", "method": "IC", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "NEGATIVE", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 296, "testName": "HBsAg (CARD)", "test_profile": "HBsAg (CARD)", "test_price": 200, "department": "SEROLOGY", "hmsCode": "001128", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NEGATIVE", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IC", "instructions": null, "notes": "Note\n\n HBsAg test is an in-vitro immunochromatographic, one step assay designed for qualitative determination of HBsAg in human serum or plasma.\n\n\nA negative result does not preclude the possibility of infection with Hepatitis B Virus.\n\nOther clinically available tests (plz mention) are required if questionable results are obtained.\n\nAs with all diagnostic tests a definitive clinical diagnosis should not be based on the result of a single test, but should only be made by the physician after all clinical and laboratory findings have been evaluated.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.175290", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 80, "min_sample_qty": null, "price": 200, "reference_range": "NEGATIVE", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "HbsAg CARD", "source_sheet": "Serology", "specimen_code": 64, "test_code": "001128", "test_done_on": "all", "test_name": "HBsAg (CARD)", "updated_at": "2025-07-09T12:20:56.175293"}}], "bill_amount": 640, "other_charges": 0, "discount_percent": 0, "subtotal": 640, "discount": 0, "gst_rate": 18, "gst_amount": 115.2, "tax": 115.2, "total_amount": 640, "paid_amount": 640, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-14", "due_date": "2025-09-13", "notes": "", "branch": 1, "created_at": "2025-08-14T15:45:53.013640", "updated_at": "2025-08-14T15:45:53.013646", "tenant_id": 1, "created_by": 4}, {"id": 90, "invoice_number": "INV00090", "sid_number": "469", "patient_id": 84, "items": [{"id": 1755168131952, "test_id": 10, "test_master_id": 10, "testName": "BLEEDING TIME", "test_name": "BLEEDING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000342", "display_name": "BLEEDING TIME", "short_name": "BT", "international_code": "", "method": "IVY", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "1 to 6", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 10, "testName": "BLEEDING TIME", "test_profile": "BLEEDING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000342", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "1 to 6", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IVY", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639261", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 100, "min_sample_qty": null, "price": 20, "reference_range": "1 to 6", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": "BT", "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000342", "test_done_on": "all", "test_name": "BLEEDING TIME", "updated_at": "2025-07-09T12:20:55.639264"}}, {"id": 1755168138720, "test_id": 13, "test_master_id": 13, "testName": "CLOTTING TIME", "test_name": "CLOTTING TIME", "amount": 20, "price": 20, "test_price": 20, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000347", "display_name": "CLOTTING TIME", "short_name": "", "international_code": "", "method": "<PERSON> and <PERSON> method", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "5 - 10", "result_unit": "minutes", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 13, "testName": "CLOTTING TIME", "test_profile": "CLOTTING TIME", "test_price": 20, "department": "HAEMATOLOGY", "hmsCode": "000347", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "5 - 10", "resultUnit": "minutes", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "<PERSON> and <PERSON> method", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.639584", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 106, "min_sample_qty": null, "price": 20, "reference_range": "5 - 10", "reporting_days": 0, "result_type": "Pick List", "result_unit": "minutes", "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000347", "test_done_on": "all", "test_name": "CLOTTING TIME", "updated_at": "2025-07-09T12:20:55.639586"}}, {"id": 1755168149178, "test_id": 214, "test_master_id": 214, "testName": "ANTI HIV 1&2 (CARD)", "test_name": "ANTI HIV 1&2 (CARD)", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "SEROLOGY", "hms_code": "001071", "display_name": "ANTI HIV 1&2 (CARD)", "short_name": "HIV CARD", "international_code": "", "method": "IC", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "Non Reactive", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 214, "testName": "ANTI HIV 1&2 (CARD)", "test_profile": "ANTI HIV 1&2 (CARD)", "test_price": 400, "department": "SEROLOGY", "hmsCode": "001071", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Non Reactive", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IC", "instructions": null, "notes": "Note:\n     HIV-1/2 is an in-vitro, visually read, qualitative immunoassay for the detection of antibodies to HIV-1 and HIV-2 in human serum, plasma or whole blood. The test is intended as an aid to detect the antibodies to HIV-1/2 from infected individuals. HIV-1/2 test in other body fluids or pooled specimens may not give accurate results.\n\n     A negative result with HIV-1/2 does not exclude the possibility of infection with HIV. A false negative results can occur in the following circumstances 1. Low level of antibody (eg. Early seroconversion specimens) are below the detection limits of the test. 2. Infection with a variant of the virus that is less detectable. 3. HIV antibodies in the patient that don’t react with specific antigens utilized in the assay configuration. 4. HIV-infected persons taking anti-retroviral medication. For these reasons, care should be taken in interpreting negative results. Other clinical data such as symptom or risk factors should be used in conjugation with test results.\n\n     Positive specimens should be retested using another method and the results should be evaluated in light of the overall clinical evaluation before a diagnosis. - Whole blood or plasma specimen containing other than EDTA may give incorrect results.\n\n     Neonates of HIV-infected mothers may carry maternal antibodies to HIV for upto around 18 months, which may not necessarily indicate the true infection status of the newborn.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.165858", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 80, "min_sample_qty": null, "price": 400, "reference_range": "Non Reactive", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "HIV CARD", "source_sheet": "Serology", "specimen_code": 64, "test_code": "001071", "test_done_on": "all", "test_name": "ANTI HIV 1&2 (CARD)", "updated_at": "2025-07-09T12:20:56.165861"}}, {"id": 1755168161578, "test_id": 296, "test_master_id": 296, "testName": "HBsAg (CARD)", "test_name": "HBsAg (CARD)", "amount": 200, "price": 200, "test_price": 200, "quantity": 1, "department": "SEROLOGY", "hms_code": "001128", "display_name": "HBsAg (CARD)", "short_name": "HbsAg CARD", "international_code": "", "method": "IC", "primary_specimen": "Serum", "specimen": "Serum", "container": "PLAIN", "reference_range": "NEGATIVE", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 296, "testName": "HBsAg (CARD)", "test_profile": "HBsAg (CARD)", "test_price": 200, "department": "SEROLOGY", "hmsCode": "001128", "specimen": "Serum", "container": "PLAIN", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "NEGATIVE", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "IC", "instructions": null, "notes": "Note\n\n HBsAg test is an in-vitro immunochromatographic, one step assay designed for qualitative determination of HBsAg in human serum or plasma.\n\n\nA negative result does not preclude the possibility of infection with Hepatitis B Virus.\n\nOther clinically available tests (plz mention) are required if questionable results are obtained.\n\nAs with all diagnostic tests a definitive clinical diagnosis should not be based on the result of a single test, but should only be made by the physician after all clinical and laboratory findings have been evaluated.", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 15, "created_at": "2025-07-09T12:20:56.175290", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 80, "min_sample_qty": null, "price": 200, "reference_range": "NEGATIVE", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "HbsAg CARD", "source_sheet": "Serology", "specimen_code": 64, "test_code": "001128", "test_done_on": "all", "test_name": "HBsAg (CARD)", "updated_at": "2025-07-09T12:20:56.175293"}}], "bill_amount": 640, "other_charges": 0, "discount_percent": 0, "subtotal": 640, "discount": 0, "gst_rate": 18, "gst_amount": 115.2, "tax": 115.2, "total_amount": 640, "paid_amount": 640, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-14", "due_date": "2025-09-13", "notes": "", "branch": 1, "created_at": "2025-08-14T16:13:06.082938", "updated_at": "2025-08-14T16:13:06.082940", "tenant_id": 1, "created_by": 4}, {"id": 91, "invoice_number": "INV00091", "sid_number": "471", "patient_id": 69, "items": [{"id": 1755685531192, "test_id": "f66ca840-5a91-4e9f-acf0-5b6d19a05f6d", "test_master_id": "f66ca840-5a91-4e9f-acf0-5b6d19a05f6d", "testName": "liquid profile", "test_name": "liquid profile", "amount": 1, "price": 1, "test_price": 1, "quantity": 1, "department": "General", "hms_code": "", "display_name": "liquid profile", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": "f66ca840-5a91-4e9f-acf0-5b6d19a05f6d", "testName": "liquid profile", "test_profile": "liquid profile", "test_price": "01", "testItems": [{"amount": 0, "testName": "MCHC", "test_id": 42}, {"amount": 0, "testName": "SICKLING TEST", "test_id": 76}], "category": "Standard", "code": "6467", "description": "testing", "isActive": true, "type": "profile", "currentTest": {"amount": 0, "testName": "", "test_id": null}, "discount": "01", "discount_price": "01", "emergency_price": "01", "gstRate": 18, "home_visit_price": "01", "is_active": true, "procedure_code": "i0934", "test_count": "2"}}, {"id": 1755685553418, "test_id": 7, "test_master_id": 7, "testName": "ABSOLUTE LYMPHOCYTE COUNT", "test_name": "ABSOLUTE LYMPHOCYTE COUNT", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000331", "display_name": "ABSOLUTE LYMPHOCYTE COUNT", "short_name": "ALC", "international_code": "", "method": "Automated", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "EDTA Container", "reference_range": "1000 - 3000", "result_unit": "cells/cumm", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 7, "testName": "ABSOLUTE LYMPHOCYTE COUNT", "test_profile": "ABSOLUTE LYMPHOCYTE COUNT", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000331", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "1000 - 3000", "resultUnit": "cells/cumm", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "Automated", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.638920", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 9, "min_sample_qty": null, "price": 100, "reference_range": "1000 - 3000", "reporting_days": 0, "result_type": "-", "result_unit": "cells/cumm", "short_name": "ALC", "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "000331", "test_done_on": "all", "test_name": "ABSOLUTE LYMPHOCYTE COUNT", "updated_at": "2025-07-09T12:20:55.638923"}}], "bill_amount": 101, "other_charges": 0, "discount_percent": 0, "subtotal": 101, "discount": 0, "gst_rate": 18, "gst_amount": 18.18, "tax": 18.18, "total_amount": 101, "paid_amount": 101, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "notes": "", "branch": 1, "created_at": "2025-08-20T15:56:29.545920", "updated_at": "2025-08-20T15:56:29.545922", "tenant_id": 1, "created_by": 4}, {"id": 92, "invoice_number": "INV00092", "sid_number": "473", "patient_id": 51, "items": [{"id": 1755690011474, "test_id": "1535110d-a306-4d07-ade6-2c12d781d085", "test_master_id": "1535110d-a306-4d07-ade6-2c12d781d085", "testName": "testing profile", "test_name": "testing profile", "amount": 2, "price": 2, "test_price": 2, "quantity": 1, "department": "General", "hms_code": "", "display_name": "testing profile", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": "1535110d-a306-4d07-ade6-2c12d781d085", "testName": "testing profile", "test_profile": "testing profile", "test_price": "02", "testItems": [{"amount": 0, "testName": "MYOGLOBIN-SERUM", "test_id": 48}, {"amount": 0, "testName": "Chromosome Analysis - Product of Conception", "test_id": 12}], "category": "Standard", "code": "testing profile master", "description": "test", "isActive": true, "type": "profile", "currentTest": {"amount": 0, "testName": "", "test_id": null}, "discount": "02", "discount_price": "02", "emergency_price": "02", "gstRate": 18, "home_visit_price": "02", "is_active": true, "procedure_code": "90829031", "test_count": "02"}}, {"id": 1755690016910, "test_id": 8, "test_master_id": 8, "testName": "ABSOLUTE NEUTROPHIL COUNT", "test_name": "ABSOLUTE NEUTROPHIL COUNT", "amount": 100, "price": 100, "test_price": 100, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000332", "display_name": "ABSOLUTE NEUTROPHIL COUNT", "short_name": "ANC", "international_code": "", "method": "Automated                        ", "primary_specimen": "EDTA BLOOD", "specimen": "EDTA BLOOD", "container": "EDTA Container", "reference_range": "2000 - 7000", "result_unit": "cells/cumm", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 8, "testName": "ABSOLUTE NEUTROPHIL COUNT", "test_profile": "ABSOLUTE NEUTROPHIL COUNT", "test_price": 100, "department": "HAEMATOLOGY", "hmsCode": "000332", "specimen": "EDTA BLOOD", "container": "EDTA Container", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "2000 - 7000", "resultUnit": "cells/cumm", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "Automated                        ", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": 7, "created_at": "2025-07-09T12:20:55.639034", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 10, "min_sample_qty": null, "price": 100, "reference_range": "2000 - 7000", "reporting_days": 0, "result_type": "-", "result_unit": "cells/cumm", "short_name": "ANC", "source_sheet": "HAEMATOLOGY", "specimen_code": 17, "test_code": "000332", "test_done_on": "all", "test_name": "ABSOLUTE NEUTROPHIL COUNT", "updated_at": "2025-07-09T12:20:55.639036"}}], "bill_amount": 102, "other_charges": 0, "discount_percent": 0, "subtotal": 102, "discount": 0, "gst_rate": 18, "gst_amount": 18.36, "tax": 18.36, "total_amount": 102, "paid_amount": 102, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "notes": "", "branch": 1, "created_at": "2025-08-20T17:10:28.687489", "updated_at": "2025-08-20T17:10:28.687491", "tenant_id": 1, "created_by": 4}, {"id": 93, "invoice_number": "INV00093", "sid_number": "475", "patient_id": 59, "items": [{"id": 1755691707319, "test_id": "1ae4e947-5b16-4fac-9c13-c3ddc008785c", "test_master_id": "1ae4e947-5b16-4fac-9c13-c3ddc008785c", "testName": "new profile", "test_name": "new profile", "amount": 2, "price": 2, "test_price": 2, "quantity": 1, "department": "General", "hms_code": "", "display_name": "new profile", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": "1ae4e947-5b16-4fac-9c13-c3ddc008785c", "testName": "new profile", "test_profile": "new profile", "test_price": "02", "testItems": [{"amount": 0, "testName": "NASAL SMEAR FOR EOSINOPHILS", "test_id": 49}, {"amount": 0, "testName": "ESR", "test_id": 21}], "category": "Standard", "code": "P000242", "description": "test", "isActive": true, "type": "profile", "currentTest": {"amount": 0, "testName": "", "test_id": null}, "discount": "02", "discount_price": "02", "emergency_price": "02", "gstRate": 18, "home_visit_price": "02", "is_active": true, "procedure_code": "23", "test_count": "02"}}, {"id": 1755691716752, "test_id": 104, "test_master_id": 104, "testName": "URINE ALBUMIN", "test_name": "URINE ALBUMIN", "amount": 30, "price": 30, "test_price": 30, "quantity": 1, "department": "CLINICAL_PATHOLOGY", "hms_code": "000325", "display_name": "URINE ALBUMIN", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "Not present  .", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 104, "testName": "URINE ALBUMIN", "test_profile": "URINE ALBUMIN", "test_price": 30, "department": "CLINICAL_PATHOLOGY", "hmsCode": "000325", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Not present  .", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.744485", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 30, "reference_range": "Not present  .", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": null, "source_sheet": "Clinical Pathology", "specimen_code": null, "test_code": "000325", "test_done_on": "all", "test_name": "URINE ALBUMIN", "updated_at": "2025-07-09T12:20:55.744488"}}], "bill_amount": 32, "other_charges": 0, "discount_percent": 0, "subtotal": 32, "discount": 0, "gst_rate": 18, "gst_amount": 5.76, "tax": 5.76, "total_amount": 32, "paid_amount": 32, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "notes": "", "branch": 1, "created_at": "2025-08-20T17:38:47.046998", "updated_at": "2025-08-20T17:38:47.047000", "tenant_id": 1, "created_by": 4}, {"id": 94, "invoice_number": "INV00094", "sid_number": "478", "patient_id": 69, "items": [{"id": 1755692765321, "test_id": "01464b61-469c-439f-aba7-a7a6814c6406", "test_master_id": "01464b61-469c-439f-aba7-a7a6814c6406", "testName": "Liquid Profile", "test_name": "Liquid Profile", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "General", "hms_code": "", "display_name": "Liquid Profile", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": "01464b61-469c-439f-aba7-a7a6814c6406", "testName": "Liquid Profile", "test_profile": "Liquid Profile", "test_price": "400", "testItems": [{"amount": 0, "testName": "<PERSON><PERSON><PERSON><PERSON>, Total", "test_id": 445}, {"amount": 0, "testName": "Cholesterol, HDL", "test_id": 442}, {"amount": 0, "testName": "Cholesterol, LDL", "test_id": 443}, {"amount": 0, "testName": "Cholesterol/HDL Ratio", "test_id": 447}, {"amount": 0, "testName": "Cholesterol, VLDL", "test_id": 446}, {"amount": 0, "testName": "LDL/HDL Ratio", "test_id": 526}, {"amount": 0, "testName": "Triglycerides", "test_id": 583}], "category": "Standard", "code": "p1", "description": "test", "isActive": true, "type": "profile", "currentTest": {"amount": 0, "testName": "", "test_id": null}, "discount": "", "discount_price": "", "emergency_price": "", "gstRate": 18, "home_visit_price": "", "is_active": true, "procedure_code": "p1", "test_count": "7"}}], "bill_amount": 400, "other_charges": 0, "discount_percent": 0, "subtotal": 400, "discount": 0, "gst_rate": 18, "gst_amount": 72, "tax": 72, "total_amount": 400, "paid_amount": 400, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "notes": "", "branch": 1, "created_at": "2025-08-20T17:56:21.387529", "updated_at": "2025-08-20T17:56:21.387531", "tenant_id": 1, "created_by": 4}, {"id": 95, "invoice_number": "INV00095", "sid_number": "480", "patient_id": 72, "items": [{"id": 1755692855953, "test_id": "01464b61-469c-439f-aba7-a7a6814c6406", "test_master_id": "01464b61-469c-439f-aba7-a7a6814c6406", "testName": "Liquid Profile", "test_name": "Liquid Profile", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "General", "hms_code": "", "display_name": "Liquid Profile", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": "01464b61-469c-439f-aba7-a7a6814c6406", "testName": "Liquid Profile", "test_profile": "Liquid Profile", "test_price": "400", "testItems": [{"amount": 0, "testName": "<PERSON><PERSON><PERSON><PERSON>, Total", "test_id": 445}, {"amount": 0, "testName": "Cholesterol, HDL", "test_id": 442}, {"amount": 0, "testName": "Cholesterol, LDL", "test_id": 443}, {"amount": 0, "testName": "Cholesterol/HDL Ratio", "test_id": 447}, {"amount": 0, "testName": "Cholesterol, VLDL", "test_id": 446}, {"amount": 0, "testName": "LDL/HDL Ratio", "test_id": 526}, {"amount": 0, "testName": "Triglycerides", "test_id": 583}], "category": "Standard", "code": "p1", "description": "test", "isActive": true, "type": "profile", "currentTest": {"amount": 0, "testName": "", "test_id": null}, "discount": "", "discount_price": "", "emergency_price": "", "gstRate": 18, "home_visit_price": "", "is_active": true, "procedure_code": "p1", "test_count": "7"}}, {"id": 1755692881661, "test_id": 29, "test_master_id": 29, "testName": "Haemoglobin, Urine", "test_name": "Haemoglobin, Urine", "amount": 300, "price": 300, "test_price": 300, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000280", "display_name": "Haemoglobin, Urine", "short_name": 15, "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 29, "testName": "Haemoglobin, Urine", "test_profile": "Haemoglobin, Urine", "test_price": 300, "department": "HAEMATOLOGY", "hmsCode": "000280", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.641973", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": "Biochemical", "min_sample_qty": null, "price": 300, "reference_range": null, "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": 15, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000280", "test_done_on": "all", "test_name": "Haemoglobin, Urine", "updated_at": "2025-07-09T12:20:55.641991"}}], "bill_amount": 700, "other_charges": 0, "discount_percent": 0, "subtotal": 700, "discount": 0, "gst_rate": 18, "gst_amount": 126, "tax": 126, "total_amount": 700, "paid_amount": 700, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "notes": "", "branch": 1, "created_at": "2025-08-20T17:58:13.236416", "updated_at": "2025-08-20T17:58:13.236418", "tenant_id": 1, "created_by": 4}, {"id": 96, "invoice_number": "INV00096", "sid_number": "483", "patient_id": 69, "items": [{"id": 1755693659700, "test_id": "01464b61-469c-439f-aba7-a7a6814c6406", "test_master_id": "01464b61-469c-439f-aba7-a7a6814c6406", "testName": "Liquid Profile", "test_name": "Liquid Profile", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "General", "hms_code": "", "display_name": "Liquid Profile", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": "01464b61-469c-439f-aba7-a7a6814c6406", "testName": "Liquid Profile", "test_profile": "Liquid Profile", "test_price": "400", "testItems": [{"amount": 0, "testName": "<PERSON><PERSON><PERSON><PERSON>, Total", "test_id": 255}, {"amount": 0, "testName": "Cholesterol, HDL", "test_id": 252}, {"amount": 0, "testName": "Cholesterol, LDL", "test_id": 253}, {"amount": 0, "testName": "Cholesterol/HDL Ratio", "test_id": 257}, {"amount": 0, "testName": "Cholesterol, VLDL", "test_id": 256}], "category": "Standard", "code": "p1", "description": "test", "isActive": true, "type": "profile", "currentTest": {"amount": 0, "testName": "", "test_id": null}, "discount": "", "discount_price": "", "emergency_price": "", "gstRate": 18, "home_visit_price": "", "is_active": true, "procedure_code": "p1", "test_count": "5"}}], "bill_amount": 400, "other_charges": 0, "discount_percent": 0, "subtotal": 400, "discount": 0, "gst_rate": 18, "gst_amount": 72, "tax": 72, "total_amount": 400, "paid_amount": 400, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "notes": "", "branch": 1, "created_at": "2025-08-20T18:11:12.585933", "updated_at": "2025-08-20T18:11:12.585935", "tenant_id": 1, "created_by": 4}, {"id": 97, "invoice_number": "INV00097", "sid_number": "485", "patient_id": 59, "items": [{"id": 1755698053344, "test_id": 104, "test_master_id": 104, "testName": "URINE ALBUMIN", "test_name": "URINE ALBUMIN", "amount": 30, "price": 30, "test_price": 30, "quantity": 1, "department": "CLINICAL_PATHOLOGY", "hms_code": "000325", "display_name": "URINE ALBUMIN", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "Not present  .", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 104, "testName": "URINE ALBUMIN", "test_profile": "URINE ALBUMIN", "test_price": 30, "department": "CLINICAL_PATHOLOGY", "hmsCode": "000325", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Not present  .", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:55.744485", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 30, "reference_range": "Not present  .", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": null, "source_sheet": "Clinical Pathology", "specimen_code": null, "test_code": "000325", "test_done_on": "all", "test_name": "URINE ALBUMIN", "updated_at": "2025-07-09T12:20:55.744488"}}, {"id": 1755698061288, "test_id": "01464b61-469c-439f-aba7-a7a6814c6406", "test_master_id": "01464b61-469c-439f-aba7-a7a6814c6406", "testName": "Liquid Profile", "test_name": "Liquid Profile", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "General", "hms_code": "", "display_name": "Liquid Profile", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": "01464b61-469c-439f-aba7-a7a6814c6406", "testName": "Liquid Profile", "test_profile": "Liquid Profile", "test_price": "400", "testItems": [{"amount": 0, "testName": "<PERSON><PERSON><PERSON><PERSON>, Total", "test_id": 255}, {"amount": 0, "testName": "Cholesterol, HDL", "test_id": 252}, {"amount": 0, "testName": "Cholesterol, LDL", "test_id": 253}, {"amount": 0, "testName": "Cholesterol/HDL Ratio", "test_id": 257}, {"amount": 0, "testName": "Cholesterol, VLDL", "test_id": 256}], "category": "Standard", "code": "p1", "description": "test", "isActive": true, "type": "profile", "currentTest": {"amount": 0, "testName": "", "test_id": null}, "discount": "", "discount_price": "", "emergency_price": "", "gstRate": 18, "home_visit_price": "", "is_active": true, "procedure_code": "p1", "test_count": "5"}}], "bill_amount": 430, "other_charges": 0, "discount_percent": 0, "subtotal": 430, "discount": 0, "gst_rate": 18, "gst_amount": 77.4, "tax": 77.4, "total_amount": 430, "paid_amount": 430, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-20", "due_date": "2025-09-19", "notes": "", "branch": 1, "created_at": "2025-08-20T19:24:35.070258", "updated_at": "2025-08-20T19:24:35.070259", "tenant_id": 1, "created_by": 4}, {"id": 98, "invoice_number": "INV00098", "sid_number": "487", "patient_id": 51, "items": [{"id": 1755754605590, "test_id": "c1d4fe27-8f93-4a8a-80ae-62aef9af3564", "test_master_id": "c1d4fe27-8f93-4a8a-80ae-62aef9af3564", "testName": "Lipid Profile", "test_name": "Lipid Profile", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "General", "hms_code": "", "display_name": "Lipid Profile", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": "c1d4fe27-8f93-4a8a-80ae-62aef9af3564", "testName": "Lipid Profile", "test_profile": "Lipid Profile", "test_price": "400", "testItems": [{"amount": 0, "testName": "<PERSON><PERSON><PERSON><PERSON>, Total", "test_id": 445}, {"amount": 0, "testName": "Triglycerides", "test_id": 583}, {"amount": 0, "testName": "Cholesterol, HDL", "test_id": 442}, {"amount": 0, "testName": "Cholesterol, LDL", "test_id": 443}, {"amount": 0, "testName": "Cholesterol, VLDL", "test_id": 446}, {"amount": 0, "testName": "Cholesterol/HDL Ratio", "test_id": 447}, {"amount": 0, "testName": "LDL/HDL Ratio", "test_id": 526}], "category": "Standard", "code": "P00001", "description": "Lipid Profile", "isActive": true, "type": "profile", "currentTest": {"amount": 0, "testName": "", "test_id": null}, "discount": 0, "discount_price": 0, "emergency_price": 0, "home_visit_price": 0, "is_active": true, "procedure_code": "P00001", "test_count": "7"}}, {"id": 1755754615120, "test_id": 107, "test_master_id": 107, "testName": "Urine Dysmorphic RBC", "test_name": "Urine Dysmorphic RBC", "amount": 800, "price": 800, "test_price": 800, "quantity": 1, "department": "CLINICAL_PATHOLOGY", "hms_code": "001611", "display_name": "Urine Dysmorphic RBC", "short_name": "UDR", "international_code": "", "method": "Microscopic", "primary_specimen": "URINE", "specimen": "URINE", "container": "<PERSON><PERSON><PERSON>er", "reference_range": "Negative", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 107, "testName": "Urine Dysmorphic RBC", "test_profile": "Urine Dysmorphic RBC", "test_price": 800, "department": "CLINICAL_PATHOLOGY", "hmsCode": "001611", "specimen": "URINE", "container": "<PERSON><PERSON><PERSON>er", "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "Negative", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "Microscopic", "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "", "applicableTo": "Both", "isActive": true, "applicable_to": null, "container_code": 13, "created_at": "2025-07-09T12:20:55.744878", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 110, "min_sample_qty": null, "price": 800, "reference_range": "Negative", "reporting_days": 0, "result_type": "Pick List", "result_unit": null, "short_name": "UDR", "source_sheet": "Clinical Pathology", "specimen_code": 55, "test_code": "001611", "test_done_on": null, "test_name": "Urine Dysmorphic RBC", "updated_at": "2025-07-09T12:20:55.744893"}}], "bill_amount": 1200, "other_charges": 0, "discount_percent": 0, "subtotal": 1200, "discount": 0, "gst_rate": 18, "gst_amount": 216, "tax": 216, "total_amount": 1200, "paid_amount": 1200, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-21", "due_date": "2025-09-20", "notes": "", "branch": 1, "created_at": "2025-08-21T11:07:27.130694", "updated_at": "2025-08-21T11:07:27.130695", "tenant_id": 1, "created_by": 4}, {"id": 99, "invoice_number": "INV00099", "sid_number": "489", "patient_id": 76, "items": [{"id": 1755755081793, "test_id": "c1d4fe27-8f93-4a8a-80ae-62aef9af3564", "test_master_id": "c1d4fe27-8f93-4a8a-80ae-62aef9af3564", "testName": "Lipid Profile", "test_name": "Lipid Profile", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "General", "hms_code": "", "display_name": "Lipid Profile", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": "c1d4fe27-8f93-4a8a-80ae-62aef9af3564", "testName": "Lipid Profile", "test_profile": "Lipid Profile", "test_price": "400", "testItems": [{"amount": 0, "testName": "<PERSON><PERSON><PERSON><PERSON>, Total", "test_id": 445}, {"amount": 0, "testName": "Triglycerides", "test_id": 583}, {"amount": 0, "testName": "Cholesterol, HDL", "test_id": 442}, {"amount": 0, "testName": "Cholesterol, LDL", "test_id": 443}, {"amount": 0, "testName": "Cholesterol, VLDL", "test_id": 446}, {"amount": 0, "testName": "Cholesterol/HDL Ratio", "test_id": 447}, {"amount": 0, "testName": "LDL/HDL Ratio", "test_id": 526}], "category": "Standard", "code": "P00001", "description": "Lipid Profile", "isActive": true, "type": "profile", "currentTest": {"amount": 0, "testName": "", "test_id": null}, "discount": 0, "discount_price": 0, "emergency_price": 0, "home_visit_price": 0, "is_active": true, "procedure_code": "P00001", "test_count": "7"}}, {"id": 1755755092496, "test_id": 15, "test_master_id": 15, "testName": "Calcium, Urine 24Hr", "test_name": "Calcium, Urine 24Hr", "amount": 0, "price": 0, "test_price": 0, "quantity": 1, "department": "HAEMATOLOGY", "hms_code": "000040", "display_name": "Calcium, Urine 24Hr", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 15, "testName": "Calcium, Urine 24Hr", "test_profile": "Calcium, Urine 24Hr", "test_price": 0, "department": "HAEMATOLOGY", "hmsCode": "000040", "specimen": null, "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "", "resultUnit": "", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": null, "instructions": null, "notes": null, "minSampleQty": "", "testDoneOn": "", "applicableTo": "Both", "isActive": true, "applicable_to": null, "container_code": null, "created_at": "2025-07-09T12:20:55.639793", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": null, "min_sample_qty": null, "price": 0, "reference_range": null, "reporting_days": 0, "result_type": "-", "result_unit": null, "short_name": null, "source_sheet": "HAEMATOLOGY", "specimen_code": null, "test_code": "000040", "test_done_on": null, "test_name": "Calcium, Urine 24Hr", "updated_at": "2025-07-09T12:20:55.639796"}}], "bill_amount": 400, "other_charges": 0, "discount_percent": 0, "subtotal": 400, "discount": 0, "gst_rate": 18, "gst_amount": 72, "tax": 72, "total_amount": 400, "paid_amount": 400, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-21", "due_date": "2025-09-20", "notes": "", "branch": 1, "created_at": "2025-08-21T11:15:33.589211", "updated_at": "2025-08-21T11:15:33.589213", "tenant_id": 1, "created_by": 4}, {"id": 100, "invoice_number": "INV00100", "sid_number": "492", "patient_id": 67, "items": [{"id": 1755755259551, "test_id": "be21dd7a-4686-470a-bcc4-8031730d62c7", "test_master_id": "be21dd7a-4686-470a-bcc4-8031730d62c7", "testName": "Liver Function test", "test_name": "Liver Function test", "amount": 500, "price": 500, "test_price": 500, "quantity": 1, "department": "General", "hms_code": "", "display_name": "Liver Function test", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": "be21dd7a-4686-470a-bcc4-8031730d62c7", "testName": "Liver Function test", "test_profile": "Liver Function test", "test_price": "500", "testItems": [{"amount": 0, "testName": "<PERSON><PERSON><PERSON><PERSON>, Total", "test_id": 424}, {"amount": 0, "testName": "<PERSON><PERSON><PERSON><PERSON>, Direct", "test_id": 422}, {"amount": 0, "testName": "Bilirubin, Indirect", "test_id": 423}, {"amount": 0, "testName": "Aspartate aminotransferase (AST/SGOT)", "test_id": 418}, {"amount": 0, "testName": "Alanine aminotransferase (ALT/SGPT)", "test_id": 402}, {"amount": 0, "testName": "Alkaline phosphatase", "test_id": 407}, {"amount": 0, "testName": "Gamma Glutamyl-Transferase (GGT)", "test_id": 417}, {"amount": 0, "testName": "Total Protein.", "test_id": 578}, {"amount": 0, "testName": "Albumin", "test_id": 403}, {"amount": 0, "testName": "Globulin", "test_id": 485}, {"amount": 0, "testName": "Albumin/Globulin", "test_id": 404}], "category": "Standard", "code": "P00002", "description": "Liver Function test", "isActive": true, "type": "profile", "currentTest": {"amount": 0, "testName": "", "test_id": null}, "discount": 0, "discount_price": 0, "emergency_price": 0, "home_visit_price": 0, "is_active": true, "procedure_code": "P00002", "test_count": "11"}}, {"id": 1755755267942, "test_id": 542, "test_master_id": 542, "testName": "<PERSON>og<PERSON>bin, Urine", "test_name": "<PERSON>og<PERSON>bin, Urine", "amount": 700, "price": 700, "test_price": 700, "quantity": 1, "department": "BIOCHEMISTRY", "hms_code": "000279", "display_name": "<PERSON>og<PERSON>bin, Urine", "short_name": "MYOU", "international_code": "", "method": "ECLIA", "primary_specimen": "URINE", "specimen": "URINE", "container": "", "reference_range": "0 - 1000", "result_unit": "ug/L", "decimals": 0, "critical_low": null, "critical_high": null, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "all", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": 542, "testName": "<PERSON>og<PERSON>bin, Urine", "test_profile": "<PERSON>og<PERSON>bin, Urine", "test_price": 700, "department": "BIOCHEMISTRY", "hmsCode": "000279", "specimen": "URINE", "container": null, "serviceTime": "", "reportingDays": "", "cutoffTime": "", "referenceRange": "0 - 1000", "resultUnit": "ug/L", "decimals": 0, "criticalLow": null, "criticalHigh": null, "method": "ECLIA", "instructions": null, "notes": "INTERPRETATION :-  Patients with urine myoglobin greater than 15,000 µg/L are at risk of acute renal failure. Results between 1,000 and 15,000 µg/L may be associated with the following conditionscrush injury, myocardial infarction, electric shock, post convulsions, sea snake bite, progressive muscle diseases like polymyositis, dermatomyositis, SLE and muscular dystrophy, drugs like cocaine, heroin, methadone,diazepam, amphetamines, barbiturates, carbon monoxide poisoning.\n\n ASSOCIATED TEST : - serum myoglobin, serum creatine phosphokinase (CPK)\n\n NOTE:  Patients on Biotin supplement may have interference in some immunoassays. With individuals taking high dose Biotin (more than 5 mg per day) supplements, at least 8-hour wait time before blood draw is recommended", "minSampleQty": "", "testDoneOn": "all", "applicableTo": "Both", "isActive": true, "applicable_to": "Both", "container_code": null, "created_at": "2025-07-09T12:20:56.550452", "critical_high": null, "critical_low": null, "excel_source": true, "is_active": true, "method_code": 52, "min_sample_qty": null, "price": 700, "reference_range": "0 - 1000", "reporting_days": 0, "result_type": "Pick List", "result_unit": "ug/L", "short_name": "MYOU", "source_sheet": "BioChemistry", "specimen_code": 55, "test_code": "000279", "test_done_on": "all", "test_name": "<PERSON>og<PERSON>bin, Urine", "updated_at": "2025-07-09T12:20:56.550455"}}], "bill_amount": 1200, "other_charges": 0, "discount_percent": 0, "subtotal": 1200, "discount": 0, "gst_rate": 18, "gst_amount": 216, "tax": 216, "total_amount": 1200, "paid_amount": 1200, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-21", "due_date": "2025-09-20", "notes": "", "branch": 1, "created_at": "2025-08-21T11:18:01.823702", "updated_at": "2025-08-21T11:18:01.823704", "tenant_id": 1, "created_by": 4}, {"id": 101, "invoice_number": "INV00101", "sid_number": "494", "patient_id": 51, "items": [{"id": 1755756992006, "test_id": "c1d4fe27-8f93-4a8a-80ae-62aef9af3564", "test_master_id": "c1d4fe27-8f93-4a8a-80ae-62aef9af3564", "testName": "Lipid Profile", "test_name": "Lipid Profile", "amount": 400, "price": 400, "test_price": 400, "quantity": 1, "department": "General", "hms_code": "", "display_name": "Lipid Profile", "short_name": "", "international_code": "", "method": "", "primary_specimen": "", "specimen": "", "container": "", "reference_range": "", "result_unit": "", "decimals": 0, "service_time": "", "reporting_days": 0, "cutoff_time": "", "min_sample_qty": "", "test_done_on": "", "applicable_to": "Both", "instructions": "", "interpretation": "", "unacceptable_conditions": "", "test_suffix": "", "suffix_desc": "", "test_master_data": {"id": "c1d4fe27-8f93-4a8a-80ae-62aef9af3564", "testName": "Lipid Profile", "test_profile": "Lipid Profile", "test_price": "400", "testItems": [{"amount": 0, "testName": "<PERSON><PERSON><PERSON><PERSON>, Total", "test_id": 255}, {"amount": 0, "testName": "Triglycerides", "test_id": 583}, {"amount": 0, "testName": "Cholesterol, HDL", "test_id": 252}, {"amount": 0, "testName": "Cholesterol, LDL", "test_id": 253}, {"amount": 0, "testName": "Cholesterol, VLDL", "test_id": 256}, {"amount": 0, "testName": "Cholesterol/HDL Ratio", "test_id": 257}, {"amount": 0, "testName": "LDL/HDL Ratio", "test_id": 526}], "category": "Standard", "code": "P00001", "description": "Lipid Profile", "isActive": true, "type": "profile", "currentTest": {"amount": 0, "testName": "", "test_id": null}, "discount": 0, "discount_price": 0, "emergency_price": 0, "home_visit_price": 0, "is_active": true, "procedure_code": "P00001", "test_count": "7"}}], "bill_amount": 400, "other_charges": 0, "discount_percent": 0, "subtotal": 400, "discount": 0, "gst_rate": 18, "gst_amount": 72, "tax": 72, "total_amount": 400, "paid_amount": 400, "balance": 0, "payment_method": "", "payment_status": "Paid", "status": "Pending", "invoice_date": "2025-08-21", "due_date": "2025-09-20", "notes": "", "branch": 1, "created_at": "2025-08-21T11:46:54.281422", "updated_at": "2025-08-21T11:46:54.281424", "tenant_id": 1, "created_by": 4}]